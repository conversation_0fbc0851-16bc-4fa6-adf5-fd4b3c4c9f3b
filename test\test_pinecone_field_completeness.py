#!/usr/bin/env python3
"""
Test Pinecone Field Completeness
Detailed inspection of all required fields in migrated Pinecone data
"""

import sys
import os
from pathlib import Path
import json
from typing import Dict, List, Any

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def inspect_pinecone_field_completeness():
    """Detailed inspection of all required fields in Pinecone"""
    
    print("🔍 Pinecone Field Completeness Inspection")
    print("=" * 70)
    
    # Required fields to check
    required_fields = [
        'call_id',
        'tran_text',  # This should be in the vector content, not metadata
        'agent_diarize_transcript',  # Should be in content
        'client_diarize_transcript',  # Should be in content
        'speaker_diarize_transcript',  # Should be in content
        'diarize_transcript',  # Should be in content
        'folder_id',
        'transcribe_date',
        'mongodb_id',
        'source',
        'autoqa_managed'
    ]
    
    try:
        db_connections = DatabaseConnections()
        
        # Get Pinecone index
        index = db_connections.get_pinecone_index()
        
        # Get all vectors with metadata
        print("\n📊 RETRIEVING ALL MIGRATED VECTORS:")
        print("-" * 50)
        
        all_vectors = index.query(
            vector=[0.0] * 1536,
            top_k=20,  # Get all 10 vectors
            include_metadata=True,
            include_values=False
        )
        
        print(f"Found {len(all_vectors.matches)} vectors")
        
        if not all_vectors.matches:
            print("❌ No vectors found in Pinecone!")
            return False
        
        # Analyze each vector
        print(f"\n📋 DETAILED FIELD ANALYSIS:")
        print("-" * 50)
        
        field_coverage = {}
        missing_fields_by_vector = {}
        
        for i, vector in enumerate(all_vectors.matches, 1):
            print(f"\n--- Vector {i}: {vector.id} ---")
            
            if not vector.metadata:
                print("❌ No metadata found!")
                missing_fields_by_vector[vector.id] = required_fields.copy()
                continue
            
            # Check each required field
            missing_fields = []
            present_fields = []
            
            for field in required_fields:
                if field in ['tran_text', 'agent_diarize_transcript', 'client_diarize_transcript', 
                           'speaker_diarize_transcript', 'diarize_transcript']:
                    # These should be in the vector content, not metadata
                    # We'll check if we have indicators that they were processed
                    if field == 'tran_text' and 'transcript_length' in vector.metadata:
                        present_fields.append(f"{field} (length: {vector.metadata['transcript_length']})")
                        field_coverage[field] = field_coverage.get(field, 0) + 1
                    elif field in ['agent_diarize_transcript', 'client_diarize_transcript', 'speaker_diarize_transcript']:
                        type_field = field.replace('_transcript', '_type')
                        if type_field in vector.metadata:
                            present_fields.append(f"{field} (type: {vector.metadata[type_field]})")
                            field_coverage[field] = field_coverage.get(field, 0) + 1
                        else:
                            missing_fields.append(field)
                    elif field == 'diarize_transcript' and 'conversation_segments' in vector.metadata:
                        present_fields.append(f"{field} (segments: {vector.metadata['conversation_segments']})")
                        field_coverage[field] = field_coverage.get(field, 0) + 1
                    else:
                        missing_fields.append(field)
                else:
                    # Regular metadata fields
                    if field in vector.metadata:
                        value = vector.metadata[field]
                        if isinstance(value, str) and len(value) > 50:
                            display_value = value[:50] + "..."
                        else:
                            display_value = value
                        present_fields.append(f"{field}: {display_value}")
                        field_coverage[field] = field_coverage.get(field, 0) + 1
                    else:
                        missing_fields.append(field)
            
            # Display results for this vector
            if present_fields:
                print("✅ Present fields:")
                for field in present_fields:
                    print(f"   {field}")
            
            if missing_fields:
                print("❌ Missing fields:")
                for field in missing_fields:
                    print(f"   {field}")
                missing_fields_by_vector[vector.id] = missing_fields
            else:
                print("✅ All required fields present!")
        
        # Summary analysis
        print(f"\n📊 FIELD COVERAGE SUMMARY:")
        print("=" * 50)
        
        total_vectors = len(all_vectors.matches)
        all_fields_complete = True
        
        for field in required_fields:
            coverage = field_coverage.get(field, 0)
            percentage = (coverage / total_vectors) * 100
            status = "✅" if percentage == 100 else "❌"
            
            print(f"{status} {field:<30} {coverage}/{total_vectors} ({percentage:.0f}%)")
            
            if percentage < 100:
                all_fields_complete = False
        
        # Check what's actually in MongoDB for comparison
        print(f"\n🔄 MONGODB SOURCE COMPARISON:")
        print("-" * 50)
        
        collection = db_connections.get_mongodb_collection('call_smart_speech_transcribe')
        sample_doc = collection.find_one()
        
        if sample_doc:
            print("Available fields in MongoDB source:")
            mongodb_fields = []
            for key, value in sample_doc.items():
                if key == '_id':
                    continue
                
                if isinstance(value, dict):
                    mongodb_fields.append(f"{key} (dict with keys: {list(value.keys())})")
                elif isinstance(value, list):
                    mongodb_fields.append(f"{key} (list with {len(value)} items)")
                else:
                    mongodb_fields.append(f"{key} ({type(value).__name__})")
            
            for field in sorted(mongodb_fields):
                print(f"   {field}")
        
        # Check for folder_id specifically
        print(f"\n🔍 FOLDER_ID SPECIFIC CHECK:")
        print("-" * 50)
        
        folder_id_found = False
        for doc in collection.find().limit(5):
            if 'folder_id' in doc:
                folder_id_found = True
                print(f"✅ folder_id found in MongoDB: {doc['folder_id']}")
                break
        
        if not folder_id_found:
            print("❌ folder_id not found in MongoDB documents")
            print("Available fields that might contain folder info:")
            for key in sample_doc.keys():
                if 'folder' in key.lower() or 'id' in key.lower():
                    print(f"   {key}: {sample_doc[key]}")
        
        # Final assessment
        print(f"\n📋 FINAL ASSESSMENT:")
        print("=" * 50)
        
        if all_fields_complete:
            print("✅ ALL REQUIRED FIELDS ARE PROPERLY STORED!")
        else:
            print("❌ SOME REQUIRED FIELDS ARE MISSING:")
            
            # Show which vectors are missing which fields
            if missing_fields_by_vector:
                print("\nMissing fields by vector:")
                for vector_id, missing in missing_fields_by_vector.items():
                    if missing:
                        print(f"   {vector_id}: {', '.join(missing)}")
        
        print(f"\n🎯 RECOMMENDATIONS:")
        print("-" * 30)
        
        if 'folder_id' not in field_coverage or field_coverage['folder_id'] == 0:
            print("• folder_id is missing - check if this field exists in MongoDB")
            print("• Update migration script to include folder_id if available")
        
        if field_coverage.get('tran_text', 0) < total_vectors:
            print("• Some vectors missing transcript content")
            print("• Verify tran_text field extraction in migration script")
        
        print("• Consider running migration with --verbose to see detailed processing")
        print("• Check migration logs for any field extraction warnings")
        
        db_connections.close_connections()
        return all_fields_complete
        
    except Exception as e:
        print(f"❌ Inspection failed: {e}")
        return False

def check_vector_content_quality():
    """Check the actual content stored in vectors"""
    
    print(f"\n🔍 VECTOR CONTENT QUALITY CHECK:")
    print("=" * 50)
    
    try:
        db_connections = DatabaseConnections()
        index = db_connections.get_pinecone_index()
        
        # Get one vector with full content
        sample_vector = index.query(
            vector=[0.0] * 1536,
            top_k=1,
            include_metadata=True,
            include_values=True
        )
        
        if sample_vector.matches:
            vector = sample_vector.matches[0]
            
            print(f"Sample Vector: {vector.id}")
            print(f"Vector Dimensions: {len(vector.values) if vector.values else 'Not available'}")
            
            if vector.metadata:
                print(f"Metadata Fields: {len(vector.metadata)}")
                
                # Check for content indicators
                content_indicators = [
                    'transcript_length',
                    'conversation_segments',
                    'agent_type',
                    'client_type',
                    'speaker_type'
                ]
                
                print("Content Quality Indicators:")
                for indicator in content_indicators:
                    if indicator in vector.metadata:
                        print(f"   ✅ {indicator}: {vector.metadata[indicator]}")
                    else:
                        print(f"   ❌ {indicator}: Missing")
        
        db_connections.close_connections()
        
    except Exception as e:
        print(f"❌ Content quality check failed: {e}")

if __name__ == "__main__":
    print("Starting comprehensive Pinecone field inspection...")
    
    success = inspect_pinecone_field_completeness()
    check_vector_content_quality()
    
    if success:
        print(f"\n🎉 Field completeness check passed!")
    else:
        print(f"\n⚠️  Some required fields are missing.")
        print(f"Consider updating the migration script to include missing fields.")
    
    print(f"\nFor detailed migration logs, check: logs/migration.log")
