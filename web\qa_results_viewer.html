<!DOCTYPE html>
<html>
<head>
    <title>AutoQA Results Viewer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .score { font-weight: bold; }
        .score-5 { color: orange; }
        .score-6 { color: blue; }
        .score-7 { color: green; }
        .score-8 { color: darkgreen; }
        .score-9 { color: darkgreen; }
        .score-10 { color: darkgreen; }
        .details { max-width: 300px; overflow: hidden; text-overflow: ellipsis; }
        .refresh-btn { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>AutoQA Results Viewer</h1>
    
    <div id="status" class="status"></div>
    
    <button class="refresh-btn" onclick="loadResults()">Refresh Results</button>
    
    <div id="summary">
        <h2>Summary</h2>
        <p>Total QA Analyses: <span id="total-count">Loading...</span></p>
        <p>Average Score: <span id="avg-score">Loading...</span></p>
        <p>Last Updated: <span id="last-updated">Loading...</span></p>
    </div>
    
    <div id="results">
        <h2>QA Analysis Results</h2>
        <table id="results-table">
            <thead>
                <tr>
                    <th>Call ID</th>
                    <th>Overall Score</th>
                    <th>CSAT</th>
                    <th>Call Verdict</th>
                    <th>Issue Resolved</th>
                    <th>Call Topic</th>
                    <th>Sentiment</th>
                    <th>Folder ID</th>
                    <th>Analyzed At</th>
                    <th>Details</th>
                </tr>
            </thead>
            <tbody id="results-body">
                <tr><td colspan="10">Loading...</td></tr>
            </tbody>
        </table>
    </div>
    
    <div id="pinecone-status">
        <h2>AutoQA Results</h2>
        <table id="pinecone-table">
            <thead>
                <tr>
                    <th>Call ID</th>
                    <th>CSAT</th>
                    <th>Score</th>
                    <th>Verdict</th>
                    <th>Analyzed At</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="pinecone-body">
                <tr><td colspan="6">Loading...</td></tr>
            </tbody>
        </table>
    </div>

    <script>
        async function loadResults() {
            try {
                document.getElementById('status').innerHTML = 'Loading results...';
                document.getElementById('status').className = 'status';
                
                const response = await fetch('/api/qa-results');
                const data = await response.json();
                
                if (data.success) {
                    displayResults(data);
                    document.getElementById('status').innerHTML = 'Results loaded successfully';
                    document.getElementById('status').className = 'status success';
                } else {
                    throw new Error(data.error || 'Failed to load results');
                }
            } catch (error) {
                document.getElementById('status').innerHTML = 'Error: ' + error.message;
                document.getElementById('status').className = 'status error';
                console.error('Error loading results:', error);
            }
        }
        
        function displayResults(data) {
            // Update summary
            document.getElementById('total-count').textContent = data.summary.total_count;
            document.getElementById('avg-score').textContent = data.summary.avg_score;
            document.getElementById('last-updated').textContent = new Date().toLocaleString();
            
            // Update QA results table
            const tbody = document.getElementById('results-body');
            tbody.innerHTML = '';
            
            if (!data.qa_results || data.qa_results.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10">No QA results found</td></tr>';
            } else {
                data.qa_results.forEach(result => {
                    const row = document.createElement('tr');

                    // Extract comprehensive QA data
                    const overallScore = result.Scores?.['Overall Score']?.['Overall Score'] || 'N/A';
                    const csat = result.CSAT || 'N/A';
                    const callVerdict = result['Call Verdict'] || 'N/A';
                    const issueResolved = result['Issue Resolved'] || 'N/A';
                    const callTopic = result['Call Topic'] || 'N/A';
                    const sentiment = result['Call Summary']?.['Overall Customer Sentiment'] || 'N/A';

                    const scoreClass = `score score-${Math.floor(parseFloat(overallScore) / 10) || 0}`;

                    row.innerHTML = `
                        <td>${result.call_id}</td>
                        <td class="${scoreClass}">${overallScore}</td>
                        <td>${csat}</td>
                        <td>${callVerdict}</td>
                        <td>${issueResolved}</td>
                        <td class="details">${callTopic}</td>
                        <td>${sentiment}</td>
                        <td>${result.folder_id || 'N/A'}</td>
                        <td>${formatDate(result.analyzed_at)}</td>
                        <td><button onclick="showDetails('${result.call_id}')">View Details</button></td>
                    `;

                    tbody.appendChild(row);
                });
            }
            
            // Update AutoQA results table
            const pineconeBody = document.getElementById('pinecone-body');
            pineconeBody.innerHTML = '';
            
            if (!data.autoqa_qa_results || data.autoqa_qa_results.length === 0) {
                pineconeBody.innerHTML = '<tr><td colspan="6">No AutoQA QA results found</td></tr>';
            } else {
                data.autoqa_qa_results.forEach(result => {
                    const row = document.createElement('tr');

                    row.innerHTML = `
                        <td>${result.call_id}</td>
                        <td>${result.csat || 'N/A'}</td>
                        <td class="score">${result.overall_score_percentage || 'N/A'}%</td>
                        <td>${result.call_verdict || 'N/A'}</td>
                        <td>${result.analyzed_at || 'N/A'}</td>
                        <td><button onclick="showDetails('${result.call_id}')" class="btn btn-sm btn-info">View</button></td>
                    `;

                    pineconeBody.appendChild(row);
                });
            }
        }
        
        function formatField(field) {
            if (!field) return 'N/A';
            
            if (typeof field === 'object') {
                if (Array.isArray(field)) {
                    return field.join(', ');
                } else {
                    return JSON.stringify(field).substring(0, 100) + '...';
                }
            }
            
            const str = String(field);
            return str.length > 100 ? str.substring(0, 100) + '...' : str;
        }
        
        function formatDate(dateStr) {
            if (!dateStr) return 'N/A';
            try {
                return new Date(dateStr).toLocaleString();
            } catch {
                return dateStr;
            }
        }

        function showDetails(callId) {
            // Open detailed view in new window
            const detailsUrl = `/api/call/${callId}`;
            fetch(detailsUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showDetailModal(data);
                    } else {
                        alert('Error loading call details: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error loading call details: ' + error.message);
                });
        }

        function showDetailModal(data) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; overflow: auto;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white; margin: 20px auto; padding: 20px;
                width: 90%; max-width: 1000px; border-radius: 5px;
            `;

            const qaAnalysis = data.qa_analysis || {};
            const assessmentTable = qaAnalysis['Assessment Table'] || [];
            const scores = qaAnalysis.Scores || {};
            const callSummary = qaAnalysis['Call Summary'] || {};

            content.innerHTML = `
                <h2>Call Details: ${data.call_id}</h2>
                <button onclick="this.parentElement.parentElement.remove()" style="float: right;">Close</button>

                <h3>Overall Information</h3>
                <table border="1" style="width: 100%; margin: 10px 0;">
                    <tr><td><strong>CSAT:</strong></td><td>${qaAnalysis.CSAT || 'N/A'}</td></tr>
                    <tr><td><strong>Call Verdict:</strong></td><td>${qaAnalysis['Call Verdict'] || 'N/A'}</td></tr>
                    <tr><td><strong>Issue Resolved:</strong></td><td>${qaAnalysis['Issue Resolved'] || 'N/A'}</td></tr>
                    <tr><td><strong>Call Topic:</strong></td><td>${qaAnalysis['Call Topic'] || 'N/A'}</td></tr>
                    <tr><td><strong>Overall Score:</strong></td><td>${scores['Overall Score']?.['Overall Score'] || 'N/A'}</td></tr>
                </table>

                <h3>Assessment Table</h3>
                <table border="1" style="width: 100%; margin: 10px 0;">
                    <thead>
                        <tr>
                            <th>Attribute</th>
                            <th>Yes/No</th>
                            <th>Points</th>
                            <th>Possible</th>
                            <th>Reasons</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${assessmentTable.map(item => `
                            <tr>
                                <td>${item.Attribute || 'N/A'}</td>
                                <td>${item['Achieved Yes/No'] || 'N/A'}</td>
                                <td>${item['Achieved Points'] || 0}</td>
                                <td>${item['Possible Points'] || 0}</td>
                                <td>${item.Reasons || 'N/A'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <h3>Call Summary</h3>
                <p><strong>Customer Sentiment:</strong> ${callSummary['Overall Customer Sentiment'] || 'N/A'}</p>
                <p><strong>Predicted Satisfaction:</strong> ${callSummary['Predicted Customer Satisfaction'] || 'N/A'}</p>

                ${callSummary['Customer Insights'] ? `
                    <h4>Customer Insights:</h4>
                    <ul>${callSummary['Customer Insights'].map(insight => `<li>${insight}</li>`).join('')}</ul>
                ` : ''}

                ${callSummary['Agent Actions'] ? `
                    <h4>Agent Actions:</h4>
                    <ul>${callSummary['Agent Actions'].map(action => `<li>${action}</li>`).join('')}</ul>
                ` : ''}

                ${callSummary['Feedback Summary for the Agent'] ? `
                    <h4>Agent Feedback:</h4>
                    <p><strong>Strengths:</strong></p>
                    <ul>${(callSummary['Feedback Summary for the Agent'].Strengths || []).map(strength => `<li>${strength}</li>`).join('')}</ul>
                    <p><strong>Areas for Improvement:</strong></p>
                    <ul>${(callSummary['Feedback Summary for the Agent']['Areas for Improvement'] || []).map(area => `<li>${area}</li>`).join('')}</ul>
                ` : ''}
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);
        }
        
        // Load results on page load
        window.onload = loadResults;
        
        // Auto-refresh every 30 seconds
        setInterval(loadResults, 30000);
    </script>
</body>
</html>
