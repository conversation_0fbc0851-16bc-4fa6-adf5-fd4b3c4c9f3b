<!DOCTYPE html>
<html>
<head>
    <title>AutoQA Results Viewer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .score { font-weight: bold; }
        .score-5 { color: orange; }
        .score-6 { color: blue; }
        .score-7 { color: green; }
        .score-8 { color: darkgreen; }
        .score-9 { color: darkgreen; }
        .score-10 { color: darkgreen; }
        .details { max-width: 300px; overflow: hidden; text-overflow: ellipsis; }
        .refresh-btn { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>AutoQA Results Viewer</h1>
    
    <div id="status" class="status"></div>
    
    <button class="refresh-btn" onclick="loadResults()">Refresh Results</button>
    
    <div id="summary">
        <h2>Summary</h2>
        <p>Total QA Analyses: <span id="total-count">Loading...</span></p>
        <p>Average Score: <span id="avg-score">Loading...</span></p>
        <p>Last Updated: <span id="last-updated">Loading...</span></p>
    </div>
    
    <div id="results">
        <h2>QA Analysis Results</h2>
        <table id="results-table">
            <thead>
                <tr>
                    <th>Call ID</th>
                    <th>QA Score</th>
                    <th>Folder ID</th>
                    <th>Analyzed At</th>
                    <th>Agent Performance</th>
                    <th>Customer Satisfaction</th>
                    <th>Key Topics</th>
                    <th>Recommendations</th>
                </tr>
            </thead>
            <tbody id="results-body">
                <tr><td colspan="8">Loading...</td></tr>
            </tbody>
        </table>
    </div>
    
    <div id="pinecone-status">
        <h2>Pinecone Status</h2>
        <table id="pinecone-table">
            <thead>
                <tr>
                    <th>Call ID</th>
                    <th>QA Analyzed</th>
                    <th>QA Score</th>
                    <th>Method</th>
                    <th>Transcript Length</th>
                    <th>Folder ID</th>
                </tr>
            </thead>
            <tbody id="pinecone-body">
                <tr><td colspan="6">Loading...</td></tr>
            </tbody>
        </table>
    </div>

    <script>
        async function loadResults() {
            try {
                document.getElementById('status').innerHTML = 'Loading results...';
                document.getElementById('status').className = 'status';
                
                const response = await fetch('/api/qa-results');
                const data = await response.json();
                
                if (data.success) {
                    displayResults(data);
                    document.getElementById('status').innerHTML = 'Results loaded successfully';
                    document.getElementById('status').className = 'status success';
                } else {
                    throw new Error(data.error || 'Failed to load results');
                }
            } catch (error) {
                document.getElementById('status').innerHTML = 'Error: ' + error.message;
                document.getElementById('status').className = 'status error';
                console.error('Error loading results:', error);
            }
        }
        
        function displayResults(data) {
            // Update summary
            document.getElementById('total-count').textContent = data.summary.total_count;
            document.getElementById('avg-score').textContent = data.summary.avg_score;
            document.getElementById('last-updated').textContent = new Date().toLocaleString();
            
            // Update QA results table
            const tbody = document.getElementById('results-body');
            tbody.innerHTML = '';
            
            if (data.qa_results.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8">No QA results found</td></tr>';
            } else {
                data.qa_results.forEach(result => {
                    const row = document.createElement('tr');
                    
                    const scoreClass = `score score-${Math.floor(result.qa_score || 0)}`;
                    
                    row.innerHTML = `
                        <td>${result.call_id}</td>
                        <td class="${scoreClass}">${result.qa_score || 'N/A'}</td>
                        <td>${result.folder_id || 'N/A'}</td>
                        <td>${formatDate(result.analyzed_at)}</td>
                        <td class="details">${formatField(result.agent_performance)}</td>
                        <td class="details">${formatField(result.customer_satisfaction)}</td>
                        <td class="details">${formatField(result.key_topics)}</td>
                        <td class="details">${formatField(result.recommendations)}</td>
                    `;
                    
                    tbody.appendChild(row);
                });
            }
            
            // Update Pinecone status table
            const pineconeBody = document.getElementById('pinecone-body');
            pineconeBody.innerHTML = '';
            
            if (data.pinecone_status.length === 0) {
                pineconeBody.innerHTML = '<tr><td colspan="6">No Pinecone data found</td></tr>';
            } else {
                data.pinecone_status.forEach(status => {
                    const row = document.createElement('tr');
                    
                    row.innerHTML = `
                        <td>${status.call_id}</td>
                        <td>${status.qa_analyzed ? 'Yes' : 'No'}</td>
                        <td class="score">${status.qa_score || 'N/A'}</td>
                        <td>${status.qa_method || 'N/A'}</td>
                        <td>${status.transcript_length || 'N/A'}</td>
                        <td>${status.folder_id || 'N/A'}</td>
                    `;
                    
                    pineconeBody.appendChild(row);
                });
            }
        }
        
        function formatField(field) {
            if (!field) return 'N/A';
            
            if (typeof field === 'object') {
                if (Array.isArray(field)) {
                    return field.join(', ');
                } else {
                    return JSON.stringify(field).substring(0, 100) + '...';
                }
            }
            
            const str = String(field);
            return str.length > 100 ? str.substring(0, 100) + '...' : str;
        }
        
        function formatDate(dateStr) {
            if (!dateStr) return 'N/A';
            try {
                return new Date(dateStr).toLocaleString();
            } catch {
                return dateStr;
            }
        }
        
        // Load results on page load
        window.onload = loadResults;
        
        // Auto-refresh every 30 seconds
        setInterval(loadResults, 30000);
    </script>
</body>
</html>
