#!/usr/bin/env python3
"""
Debug OpenAI Response Structure
Investigate what GPT-4o-mini is actually returning
"""

import sys
import os
from pathlib import Path
import json

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def debug_openai_response():
    """Debug the actual OpenAI response structure"""
    
    print("🔍 Debugging OpenAI Response Structure")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        qa_collection = db.get_mongodb_collection('qa_analysis')
        
        # Get the latest QA analysis
        latest_qa = qa_collection.find_one(sort=[('analyzed_at', -1)])
        
        if not latest_qa:
            print("❌ No QA analysis found!")
            return
        
        print(f"📋 Latest QA Analysis Debug:")
        print(f"Call ID: {latest_qa.get('call_id')}")
        print(f"Analysis Method: {latest_qa.get('analysis_method')}")
        print(f"Analyzed At: {latest_qa.get('analyzed_at')}")
        
        # Check if we have raw response
        if 'raw_response' in latest_qa:
            raw_response = latest_qa['raw_response']
            print(f"\n📝 Raw OpenAI Response:")
            print(f"Length: {len(raw_response)} characters")
            print(f"Type: {type(raw_response)}")
            
            # Show first 500 characters
            print(f"\n📄 Response Preview (first 500 chars):")
            print("-" * 50)
            print(raw_response[:500])
            print("-" * 50)
            
            # Try to parse as JSON
            print(f"\n🔧 JSON Parsing Test:")
            try:
                # Clean and parse
                json_content = raw_response.strip()
                if '```json' in json_content:
                    json_start = json_content.find('```json') + 7
                    json_end = json_content.find('```', json_start)
                    json_content = json_content[json_start:json_end].strip()
                
                parsed_json = json.loads(json_content)
                print(f"✅ JSON parsing successful!")
                print(f"Top-level keys: {list(parsed_json.keys())}")
                
                # Check for Scores structure
                if 'Scores' in parsed_json:
                    scores = parsed_json['Scores']
                    print(f"\n🎯 Scores Structure:")
                    print(f"Scores type: {type(scores)}")
                    print(f"Scores content: {scores}")
                    
                    if isinstance(scores, dict) and 'Overall Score' in scores:
                        overall_score = scores['Overall Score']
                        print(f"Overall Score type: {type(overall_score)}")
                        print(f"Overall Score content: {overall_score}")
                        
                        if isinstance(overall_score, dict) and 'Overall Score' in overall_score:
                            final_score = overall_score['Overall Score']
                            print(f"Final Score: {final_score} (type: {type(final_score)})")
                        else:
                            print(f"❌ Missing nested 'Overall Score' key")
                    else:
                        print(f"❌ Missing 'Overall Score' in Scores")
                else:
                    print(f"❌ No 'Scores' key found in response")
                    print(f"Available keys: {list(parsed_json.keys())}")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                print(f"Attempting to find JSON structure...")
                
                # Look for JSON-like patterns
                if '{' in raw_response and '}' in raw_response:
                    start_idx = raw_response.find('{')
                    end_idx = raw_response.rfind('}') + 1
                    potential_json = raw_response[start_idx:end_idx]
                    print(f"Potential JSON section: {potential_json[:200]}...")
        else:
            print(f"❌ No raw_response field found!")
        
        # Check parsed fields
        print(f"\n📊 Parsed QA Fields:")
        qa_fields = ['CSAT', 'Call Verdict', 'Issue Resolved', 'overall_score_numeric']
        for field in qa_fields:
            value = latest_qa.get(field, 'MISSING')
            print(f"  {field}: {value}")
        
        # Check if parsing error occurred
        if 'parsing_error' in latest_qa:
            print(f"\n❌ Parsing Error: {latest_qa['parsing_error']}")
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_openai_response()
