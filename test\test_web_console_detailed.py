#!/usr/bin/env python3
"""
Test Web Console with Detailed Error Capture
"""

import sys
import os
import json
import requests
import time
from pathlib import Path

def test_web_console_detailed():
    """Test web console and capture detailed errors"""
    
    print("🧪 Testing Web Console (Detailed Error Capture)")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    try:
        # Test health endpoint first
        print("📋 Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health endpoint working")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
        
        # Test main QA results API
        print("\n📋 Testing QA results API...")
        response = requests.get(f"{base_url}/api/qa-results", timeout=15)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ QA results API response received")
                print(f"  Success: {data.get('success')}")
                
                # Check for errors in response
                if 'error' in data:
                    print(f"❌ API returned error: {data['error']}")
                    return False
                
                # Check summary data
                summary = data.get('summary', {})
                print(f"  Summary: {summary}")
                
                # Check AutoQA data
                autoqa_qa_results = data.get('autoqa_qa_results', [])
                autoqa_transcripts = data.get('autoqa_transcripts', [])
                
                print(f"  AutoQA QA results count: {len(autoqa_qa_results)}")
                print(f"  AutoQA transcripts count: {len(autoqa_transcripts)}")
                
                if autoqa_qa_results:
                    sample = autoqa_qa_results[0]
                    print(f"  Sample QA result keys: {list(sample.keys())}")
                    
                    # Check for undefined/null values that might cause length errors
                    problematic_fields = []
                    for key, value in sample.items():
                        if value is None:
                            problematic_fields.append(f"{key}=None")
                        elif isinstance(value, str) and value == 'null':
                            problematic_fields.append(f"{key}='null'")
                        elif isinstance(value, str) and value == 'undefined':
                            problematic_fields.append(f"{key}='undefined'")
                    
                    if problematic_fields:
                        print(f"⚠️ Fields with problematic values: {problematic_fields}")
                    else:
                        print("✅ No problematic values found in sample")
                    
                    # Test specific call details
                    call_id = sample.get('call_id')
                    if call_id:
                        print(f"\n📋 Testing call details for {call_id}...")
                        detail_response = requests.get(f"{base_url}/api/call/{call_id}", timeout=10)
                        
                        print(f"Call details status: {detail_response.status_code}")
                        
                        if detail_response.status_code == 200:
                            try:
                                detail_data = detail_response.json()
                                print("✅ Call details API working")
                                
                                # Check AutoQA data in details
                                autoqa_qa_data = detail_data.get('autoqa_qa_data')
                                if autoqa_qa_data:
                                    print("✅ AutoQA data found in call details")
                                    
                                    # Check for problematic fields in detail data
                                    detail_problematic = []
                                    for key, value in autoqa_qa_data.items():
                                        if value is None:
                                            detail_problematic.append(f"{key}=None")
                                        elif isinstance(value, str) and value in ['null', 'undefined']:
                                            detail_problematic.append(f"{key}='{value}'")
                                    
                                    if detail_problematic:
                                        print(f"⚠️ Problematic fields in call details: {detail_problematic}")
                                    else:
                                        print("✅ No problematic values in call details")
                                    
                                    # Check parsed JSON fields
                                    json_fields = ['full_qa_result_parsed', 'assessment_table_parsed', 'agent_feedback_parsed', 'call_summary_parsed', 'scores_parsed']
                                    for field in json_fields:
                                        if field in autoqa_qa_data:
                                            value = autoqa_qa_data[field]
                                            if value is None:
                                                print(f"⚠️ {field}: None")
                                            elif isinstance(value, (list, dict)):
                                                print(f"✅ {field}: {type(value).__name__} with {len(value)} items")
                                            else:
                                                print(f"⚠️ {field}: {type(value).__name__} = {value}")
                                        else:
                                            print(f"❌ {field}: Missing")
                                else:
                                    print("❌ No AutoQA data in call details")
                                    return False
                                    
                            except json.JSONDecodeError as e:
                                print(f"❌ Failed to parse call details JSON: {e}")
                                print(f"Response content: {detail_response.text[:500]}...")
                                return False
                        else:
                            print(f"❌ Call details API failed: {detail_response.status_code}")
                            print(f"Response: {detail_response.text[:500]}...")
                            return False
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Response content: {response.text[:500]}...")
                return False
        else:
            print(f"❌ QA results API failed: {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Web console request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_browser_access():
    """Test browser access to the main page"""
    
    print("\n🌐 Testing Browser Access")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    try:
        # Test main page
        print("📋 Testing main page...")
        response = requests.get(base_url, timeout=10)
        
        print(f"Main page status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"Content length: {len(content)} chars")
            
            # Check for JavaScript errors in the HTML
            if 'error' in content.lower():
                print("⚠️ Found 'error' in page content")
                # Find error context
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'error' in line.lower():
                        print(f"  Line {i+1}: {line.strip()}")
            
            # Check for undefined references
            if 'undefined' in content:
                print("⚠️ Found 'undefined' in page content")
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'undefined' in line:
                        print(f"  Line {i+1}: {line.strip()}")
            
            print("✅ Main page accessible")
            return True
        else:
            print(f"❌ Main page failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Browser access test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Detailed Web Console Test")
    print("=" * 70)
    
    # Test API endpoints
    api_success = test_web_console_detailed()
    
    # Test browser access
    browser_success = test_browser_access()
    
    print("\n" + "=" * 70)
    print("📊 DETAILED WEB CONSOLE TEST SUMMARY:")
    print(f"  API Endpoints: {'✅ PASSED' if api_success else '❌ FAILED'}")
    print(f"  Browser Access: {'✅ PASSED' if browser_success else '❌ FAILED'}")
    
    if api_success and browser_success:
        print("\n🎉 WEB CONSOLE WORKING CORRECTLY!")
        print("✅ No 'undefined length' errors found!")
        print("✅ All data is properly structured!")
    else:
        print("\n❌ WEB CONSOLE HAS ISSUES!")
        print("🔍 Check the detailed output above for specific problems.")
        sys.exit(1)
