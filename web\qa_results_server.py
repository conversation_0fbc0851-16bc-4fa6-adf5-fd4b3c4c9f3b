#!/usr/bin/env python3
"""
Simple Flask server to serve QA results
Basic API for viewing AutoQA analysis results
"""

import sys
import os
from pathlib import Path
from flask import Flask, jsonify, send_from_directory
from flask_cors import CORS
import json
from datetime import datetime

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

@app.route('/')
def index():
    """Serve the main HTML page"""
    return send_from_directory('.', 'qa_results_viewer.html')

@app.route('/api/qa-results')
def get_qa_results():
    """Get QA analysis results from MongoDB and Pinecone"""
    
    try:
        db_connections = DatabaseConnections()
        
        # Get QA results from MongoDB
        qa_collection = db_connections.get_mongodb_collection('qa_analysis')
        qa_results = []
        
        for doc in qa_collection.find().sort('analyzed_at', -1):
            # Convert ObjectId to string and clean up the document
            result = {
                'call_id': doc.get('call_id'),
                'qa_score': doc.get('call_quality_score'),
                'folder_id': doc.get('folder_id'),
                'analyzed_at': doc.get('analyzed_at'),
                'analysis_method': doc.get('analysis_method'),
                'agent_performance': doc.get('agent_performance'),
                'customer_satisfaction': doc.get('customer_satisfaction'),
                'compliance_issues': doc.get('compliance_issues'),
                'key_topics': doc.get('key_topics'),
                'sentiment_analysis': doc.get('sentiment_analysis'),
                'recommendations': doc.get('recommendations'),
                'analysis_summary': doc.get('analysis_summary'),
                'transcript_length': doc.get('transcript_length')
            }
            qa_results.append(result)
        
        # Get Pinecone status
        index = db_connections.get_pinecone_index()
        
        query_result = index.query(
            vector=[0.0] * 1536,
            top_k=100,  # Get up to 100 results
            include_metadata=True,
            filter={'autoqa_managed': True}
        )
        
        pinecone_status = []
        for match in query_result.matches:
            metadata = match.metadata
            status = {
                'call_id': metadata.get('call_id'),
                'qa_analyzed': metadata.get('qa_analyzed', False),
                'qa_score': metadata.get('qa_score'),
                'qa_method': metadata.get('qa_method'),
                'transcript_length': metadata.get('transcript_length'),
                'folder_id': metadata.get('folder_id'),
                'conversation_segments': metadata.get('conversation_segments'),
                'migrated_at': metadata.get('migrated_at'),
                'source': metadata.get('source')
            }
            pinecone_status.append(status)
        
        # Calculate summary statistics
        total_count = len(qa_results)
        avg_score = 0
        
        if total_count > 0:
            scores = [float(r['qa_score']) for r in qa_results if r['qa_score'] and str(r['qa_score']).replace('.', '').isdigit()]
            avg_score = round(sum(scores) / len(scores), 1) if scores else 0
        
        summary = {
            'total_count': total_count,
            'avg_score': avg_score,
            'pinecone_vectors': len(pinecone_status),
            'analyzed_vectors': len([s for s in pinecone_status if s['qa_analyzed']])
        }
        
        db_connections.close_connections()
        
        return jsonify({
            'success': True,
            'summary': summary,
            'qa_results': qa_results,
            'pinecone_status': pinecone_status,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/call/<call_id>')
def get_call_details(call_id):
    """Get detailed information for a specific call"""
    
    try:
        db_connections = DatabaseConnections()
        
        # Get QA analysis
        qa_collection = db_connections.get_mongodb_collection('qa_analysis')
        qa_doc = qa_collection.find_one({'call_id': call_id})
        
        # Get original transcript
        transcript_collection = db_connections.get_mongodb_collection('call_smart_speech_transcribe')
        transcript_doc = transcript_collection.find_one({'call_id': call_id})
        
        # Get Pinecone metadata
        index = db_connections.get_pinecone_index()
        query_result = index.query(
            vector=[0.0] * 1536,
            filter={'call_id': call_id},
            top_k=1,
            include_metadata=True
        )
        
        pinecone_metadata = None
        if query_result.matches:
            pinecone_metadata = query_result.matches[0].metadata
        
        db_connections.close_connections()
        
        return jsonify({
            'success': True,
            'call_id': call_id,
            'qa_analysis': qa_doc,
            'transcript': transcript_doc,
            'pinecone_metadata': pinecone_metadata,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'call_id': call_id,
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/stats')
def get_stats():
    """Get overall statistics"""
    
    try:
        db_connections = DatabaseConnections()
        
        # MongoDB stats
        qa_collection = db_connections.get_mongodb_collection('qa_analysis')
        transcript_collection = db_connections.get_mongodb_collection('call_smart_speech_transcribe')
        
        qa_count = qa_collection.count_documents({})
        transcript_count = transcript_collection.count_documents({})
        
        # Pinecone stats
        index = db_connections.get_pinecone_index()
        pinecone_stats = index.describe_index_stats()
        
        # Score distribution
        score_distribution = {}
        for doc in qa_collection.find({}, {'call_quality_score': 1}):
            score = doc.get('call_quality_score')
            if score:
                score_key = f"Score {score}"
                score_distribution[score_key] = score_distribution.get(score_key, 0) + 1
        
        db_connections.close_connections()
        
        return jsonify({
            'success': True,
            'mongodb': {
                'qa_analyses': qa_count,
                'transcripts': transcript_count
            },
            'pinecone': {
                'total_vectors': pinecone_stats.total_vector_count,
                'dimension': pinecone_stats.dimension,
                'index_fullness': pinecone_stats.index_fullness
            },
            'score_distribution': score_distribution,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/health')
def health_check():
    """Simple health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Starting AutoQA Results Server")
    print("=" * 40)
    print("📊 Available endpoints:")
    print("  http://localhost:5000/           - Main results viewer")
    print("  http://localhost:5000/api/qa-results - QA results API")
    print("  http://localhost:5000/api/stats      - Statistics API")
    print("  http://localhost:5000/health        - Health check")
    print("=" * 40)
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False  # Disable reloader to avoid issues with imports
    )
