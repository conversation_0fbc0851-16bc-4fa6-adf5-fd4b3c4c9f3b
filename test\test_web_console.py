#!/usr/bin/env python3
"""
Test Web Console AutoQA Integration
Test the web console endpoints for AutoQA data
"""

import sys
import os
import json
import requests
import time
from pathlib import Path
from threading import Thread

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

def start_web_server():
    """Start the web server in a separate thread"""
    try:
        sys.path.append(str(project_root / "web"))
        from qa_results_server import app
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
    except Exception as e:
        print(f"Error starting web server: {e}")

def test_web_console_endpoints():
    """Test web console endpoints"""
    
    print("🧪 Testing Web Console AutoQA Integration")
    print("=" * 60)
    
    # Start web server in background
    print("📋 Starting web server...")
    server_thread = Thread(target=start_web_server, daemon=True)
    server_thread.start()
    
    # Wait for server to start
    time.sleep(3)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # Test health endpoint
        print("\n📋 Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health endpoint working")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
        
        # Test QA results API
        print("\n📋 Testing QA results API...")
        response = requests.get(f"{base_url}/api/qa-results", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ QA results API working")
            print(f"  Success: {data.get('success')}")
            print(f"  Summary: {data.get('summary', {})}")
            print(f"  AutoQA Transcripts: {len(data.get('autoqa_transcripts', []))}")
            print(f"  AutoQA QA Results: {len(data.get('autoqa_qa_results', []))}")
            
            # Check if we have AutoQA data
            if data.get('autoqa_transcripts') or data.get('autoqa_qa_results'):
                print("✅ AutoQA data found in API response")
                
                # Test specific call details if we have data
                if data.get('autoqa_qa_results'):
                    sample_call_id = data['autoqa_qa_results'][0].get('call_id')
                    if sample_call_id:
                        print(f"\n📋 Testing call details for {sample_call_id}...")
                        detail_response = requests.get(f"{base_url}/api/call/{sample_call_id}", timeout=10)
                        if detail_response.status_code == 200:
                            detail_data = detail_response.json()
                            print("✅ Call details API working")
                            print(f"  Has AutoQA transcript data: {bool(detail_data.get('autoqa_transcript_data'))}")
                            print(f"  Has AutoQA QA data: {bool(detail_data.get('autoqa_qa_data'))}")
                            
                            # Check for parsed QA data
                            autoqa_qa_data = detail_data.get('autoqa_qa_data', {})
                            if autoqa_qa_data.get('full_qa_result_parsed'):
                                print("✅ Complete QA JSON data available and parsed")
                                qa_result = autoqa_qa_data['full_qa_result_parsed']
                                print(f"  QA Result keys: {list(qa_result.keys())}")
                            
                            if autoqa_qa_data.get('assessment_table_parsed'):
                                print("✅ Assessment table data available and parsed")
                                assessment = autoqa_qa_data['assessment_table_parsed']
                                print(f"  Assessment items: {len(assessment)}")
                            
                            if autoqa_qa_data.get('agent_feedback_parsed'):
                                print("✅ Agent feedback data available and parsed")
                        else:
                            print(f"❌ Call details API failed: {detail_response.status_code}")
                
                # Test AutoQA search endpoint
                if data.get('autoqa_qa_results'):
                    sample_call_id = data['autoqa_qa_results'][0].get('call_id')
                    if sample_call_id:
                        print(f"\n📋 Testing AutoQA search for {sample_call_id}...")
                        search_response = requests.get(f"{base_url}/api/autoqa/search/{sample_call_id}", timeout=10)
                        if search_response.status_code == 200:
                            search_data = search_response.json()
                            print("✅ AutoQA search API working")
                            print(f"  Found: {search_data.get('found')}")
                            print(f"  Has transcript data: {bool(search_data.get('transcript_data'))}")
                            print(f"  Has QA data: {bool(search_data.get('qa_data'))}")
                            
                            # Check for complete QA data
                            qa_data = search_data.get('qa_data', {})
                            if qa_data.get('full_qa_result'):
                                print("✅ Complete QA analysis available in search")
                                print(f"  QA keys: {list(qa_data['full_qa_result'].keys())}")
                            
                            if qa_data.get('assessment_table'):
                                print("✅ Assessment table available in search")
                                print(f"  Assessment items: {len(qa_data['assessment_table'])}")
                        else:
                            print(f"❌ AutoQA search API failed: {search_response.status_code}")
            else:
                print("⚠️ No AutoQA data found (this is expected if no data has been processed)")
        else:
            print(f"❌ QA results API failed: {response.status_code}")
            return False
        
        # Test stats endpoint
        print("\n📋 Testing stats endpoint...")
        response = requests.get(f"{base_url}/api/stats", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Stats API working")
            print(f"  MongoDB QA analyses: {data.get('mongodb', {}).get('qa_analyses', 0)}")
            print(f"  MongoDB transcripts: {data.get('mongodb', {}).get('transcripts', 0)}")
            print(f"  Pinecone status: {data.get('pinecone', {}).get('status', 'unknown')}")
        else:
            print(f"❌ Stats API failed: {response.status_code}")
        
        print("\n🎉 WEB CONSOLE TESTS COMPLETED!")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Web console test failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_web_console_offline():
    """Test web console functionality without starting server (import test)"""
    
    print("\n🧪 Testing Web Console Import and Configuration")
    print("=" * 60)
    
    try:
        # Test importing the web server module
        sys.path.append(str(project_root / "web"))
        from qa_results_server import app
        print("✅ Web server module imports successfully")
        
        # Test Flask app configuration
        if app:
            print("✅ Flask app created successfully")
            print(f"  App name: {app.name}")
            
            # Check routes
            routes = []
            for rule in app.url_map.iter_rules():
                routes.append(f"{rule.rule} ({', '.join(rule.methods)})")
            
            print(f"✅ Found {len(routes)} routes:")
            for route in routes:
                print(f"    {route}")
            
            # Check for AutoQA specific routes
            autoqa_routes = [r for r in routes if 'autoqa' in r.lower()]
            if autoqa_routes:
                print(f"✅ Found AutoQA specific routes: {len(autoqa_routes)}")
            else:
                print("⚠️ No AutoQA specific routes found")
        
        return True
        
    except Exception as e:
        print(f"❌ Web console import test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Web Console Tests")
    print("=" * 70)
    
    # Test offline functionality first
    offline_success = test_web_console_offline()
    
    # Test online functionality
    online_success = False
    try:
        online_success = test_web_console_endpoints()
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"❌ Online test failed: {e}")
    
    print("\n" + "=" * 70)
    print("📊 WEB CONSOLE TEST SUMMARY:")
    print(f"  Import/Configuration: {'✅ PASSED' if offline_success else '❌ FAILED'}")
    print(f"  API Endpoints: {'✅ PASSED' if online_success else '❌ FAILED'}")
    
    if offline_success and online_success:
        print("\n🎉 ALL WEB CONSOLE TESTS PASSED!")
        print("\n✅ Web console can import and configure successfully!")
        print("✅ AutoQA API endpoints are working!")
        print("✅ Complete QA data is accessible through web interface!")
    elif offline_success:
        print("\n⚠️ PARTIAL SUCCESS!")
        print("✅ Web console imports and configures correctly")
        print("⚠️ API endpoint tests failed (may be due to network/data issues)")
    else:
        print("\n❌ WEB CONSOLE TESTS FAILED!")
        sys.exit(1)
