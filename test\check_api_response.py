#!/usr/bin/env python3
"""
Check API Response Structure
"""

import requests
import json

try:
    response = requests.get("http://localhost:5000/api/qa-results", timeout=10)
    
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"\nAPI Response Structure:")
        print(f"Success: {data.get('success')}")
        print(f"Top-level keys: {list(data.keys())}")
        
        # Check specific fields that might be causing the length error
        problematic_fields = ['qa_results', 'pinecone_status', 'autoqa_qa_results', 'autoqa_transcripts']
        
        for field in problematic_fields:
            if field in data:
                value = data[field]
                if value is None:
                    print(f"❌ {field}: None (will cause .length error)")
                elif isinstance(value, list):
                    print(f"✅ {field}: List with {len(value)} items")
                else:
                    print(f"⚠️ {field}: {type(value).__name__} = {value}")
            else:
                print(f"❌ {field}: Missing from response")
        
        # Show sample data structure
        if 'autoqa_qa_results' in data and data['autoqa_qa_results']:
            sample = data['autoqa_qa_results'][0]
            print(f"\nSample autoqa_qa_results item keys: {list(sample.keys())}")
        
        if 'autoqa_transcripts' in data and data['autoqa_transcripts']:
            sample = data['autoqa_transcripts'][0]
            print(f"Sample autoqa_transcripts item keys: {list(sample.keys())}")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Error: {e}")
