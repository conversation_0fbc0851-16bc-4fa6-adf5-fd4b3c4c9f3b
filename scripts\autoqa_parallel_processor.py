#!/usr/bin/env python3
"""
AutoQA Parallel Processing System
Processes call transcripts using OpenAI Assistant for QA analysis
"""

import sys
import os
import json
import asyncio
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import time

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections
import openai
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutoQAParallelProcessor:
    """Parallel processor for AutoQA analysis"""
    
    def __init__(self, max_workers: int = 3):
        self.db_connections = DatabaseConnections()
        self.openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.max_workers = max_workers

        # Two-index architecture
        self.transcripts_index_name = os.getenv('PINECONE_INDEX_NAME', 'autoqa-transcripts')
        self.qa_results_index_name = os.getenv('PINECONE_QA_RESULTS_INDEX', 'autoqa-qa-results')

        # Using direct chat completion with comprehensive prompt (no Assistant API needed)
        logger.info("Using direct GPT-4o-mini chat completion with comprehensive QA prompt")
        logger.info(f"Reading from: {self.transcripts_index_name}")
        logger.info(f"Writing to: {self.qa_results_index_name}")

        logger.info(f"✅ AutoQA Parallel Processor initialized with {max_workers} workers")
    
    def get_vectors_from_pinecone(self, limit: int = None) -> List[Dict]:
        """Retrieve vectors from autoqa-transcripts index for processing"""

        logger.info(f"🔍 Retrieving vectors from {self.transcripts_index_name} (limit: {limit or 'all'})")

        index = self.db_connections.get_pinecone_index(self.transcripts_index_name)
        
        # Query to get all vectors
        query_result = index.query(
            vector=[0.0] * 1536,  # Dummy vector
            top_k=limit or 10000,  # Get all or limited
            include_metadata=True,
            include_values=False
        )
        
        vectors = []
        for match in query_result.matches:
            if match.metadata and match.metadata.get('autoqa_managed'):
                vectors.append({
                    'vector_id': match.id,
                    'call_id': match.metadata.get('call_id'),
                    'folder_id': match.metadata.get('folder_id'),
                    'transcript_length': match.metadata.get('transcript_length'),
                    'conversation_segments': match.metadata.get('conversation_segments'),
                    'metadata': match.metadata
                })
        
        logger.info(f"✅ Retrieved {len(vectors)} AutoQA-managed vectors")
        return vectors
    
    def get_full_transcript_content(self, call_id: str) -> Optional[str]:
        """Get full transcript content from MongoDB"""
        
        try:
            # Get from primary transcript collection
            collection = self.db_connections.get_mongodb_collection('call_smart_speech_transcribe')
            doc = collection.find_one({'call_id': call_id})
            
            if not doc:
                logger.warning(f"No transcript found for call_id: {call_id}")
                return None
            
            # Extract comprehensive text content
            text_parts = []
            
            # Main transcript
            if 'tran_text' in doc and doc['tran_text']:
                text_parts.append(f"COMPLETE TRANSCRIPT:\n{doc['tran_text']}")
            
            # Diarized transcripts
            diarize_fields = [
                ('agent_diarize_transcript', 'AGENT SIDE'),
                ('client_diarize_transcript', 'CLIENT SIDE'),
                ('speaker_diarize_transcript', 'SPEAKER ANALYSIS')
            ]
            
            for field, label in diarize_fields:
                if field in doc and doc[field]:
                    if isinstance(doc[field], dict) and 'text' in doc[field]:
                        text_parts.append(f"{label}:\n{doc[field]['text']}")
            
            # Conversation flow
            if 'diarize_transcript' in doc and isinstance(doc['diarize_transcript'], list):
                diarize_text = []
                for segment in doc['diarize_transcript']:
                    if isinstance(segment, dict):
                        speaker = segment.get('speaker', 'Unknown')
                        text = segment.get('text', '')
                        diarize_text.append(f"{speaker}: {text}")
                
                if diarize_text:
                    text_parts.append(f"CONVERSATION FLOW:\n" + "\n".join(diarize_text))
            
            return '\n\n'.join(text_parts)
            
        except Exception as e:
            logger.error(f"Error getting transcript for {call_id}: {e}")
            return None
    
    def load_comprehensive_qa_prompt(self) -> str:
        """Load the comprehensive QA prompt from file"""
        try:
            prompt_file = Path(__file__).parent.parent / "QA_Insight_Prompt.txt"
            with open(prompt_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.warning(f"Could not load QA prompt file: {e}")
            return self.get_fallback_prompt()

    def get_fallback_prompt(self) -> str:
        """Fallback prompt if file cannot be loaded"""
        return """
        Please analyze this call transcript for quality assurance following standard QA criteria.
        Provide a comprehensive analysis in JSON format.
        """

    def analyze_call_with_openai(self, call_id: str, transcript: str, folder_id: str) -> Dict[str, Any]:
        """Analyze call transcript using OpenAI Assistant with comprehensive QA prompt"""

        try:
            # Load comprehensive QA prompt
            base_qa_prompt = self.load_comprehensive_qa_prompt()

            # Prepare the complete prompt with transcript
            qa_prompt = f"""
{base_qa_prompt}

Call ID: {call_id}
Folder ID: {folder_id}

TRANSCRIPT TO ANALYZE:
{transcript}

Please provide your analysis following the exact format specified above, ensuring all categories and attributes are assessed according to the detailed criteria provided.
            """
            
            # Use direct chat completion with comprehensive prompt (no Assistant API)
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",  # Use cost-effective mini model
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional call quality analysis expert. Follow the provided QA criteria exactly and provide comprehensive analysis in the specified JSON format."
                    },
                    {
                        "role": "user",
                        "content": qa_prompt
                    }
                ],
                max_tokens=4000,  # Increased for comprehensive analysis
                temperature=0.1,  # Lower temperature for consistent analysis
                response_format={"type": "json_object"}  # Force JSON response
            )

            assistant_response = response.choices[0].message.content
            
            # Try to parse JSON response
            try:
                # Extract JSON from response if it's wrapped in markdown
                if '```json' in assistant_response:
                    json_start = assistant_response.find('```json') + 7
                    json_end = assistant_response.find('```', json_start)
                    json_content = assistant_response[json_start:json_end].strip()
                else:
                    json_content = assistant_response.strip()

                # Clean up the JSON content
                json_content = json_content.replace('\n', ' ').replace('\r', ' ')

                qa_analysis = json.loads(json_content)

                # Ensure we have the comprehensive structure
                if not isinstance(qa_analysis, dict):
                    raise json.JSONDecodeError("Response is not a valid JSON object", "", 0)

                logger.info(f"✅ Successfully parsed QA analysis for {call_id}")
                logger.info(f"QA Analysis keys: {list(qa_analysis.keys())}")

            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON parsing failed for {call_id}: {e}")
                logger.error(f"Raw response length: {len(assistant_response)}")
                logger.error(f"Raw response preview: {assistant_response[:200]}...")

                # Create a basic structured response with the raw content
                qa_analysis = {
                    "CSAT": "Parsing Failed",
                    "Issue Resolved": "Unknown",
                    "Call Verdict": "Analysis Error",
                    "Call Topic": "Unknown",
                    "Call Summary": {
                        "Customer Insights": ["JSON parsing failed - see raw response"],
                        "Agent Actions": ["JSON parsing failed - see raw response"],
                        "Overall Customer Sentiment": "Unknown",
                        "Predicted Customer Satisfaction": "Unknown"
                    },
                    "Assessment Table": [],
                    "Scores": {
                        "Overall Score": {
                            "Overall Score": "0%"
                        }
                    },
                    "raw_response": assistant_response,
                    "parsing_error": str(e),
                    "analysis_status": "parsing_failed"
                }
            
            # Add metadata
            qa_analysis.update({
                'call_id': call_id,
                'folder_id': folder_id,
                'analyzed_at': datetime.now().isoformat(),
                'analysis_method': 'gpt4o_mini_comprehensive_prompt',
                'transcript_length': len(transcript)
            })

            # Extract overall score for easy access
            overall_score_numeric = 0
            try:
                if 'Scores' in qa_analysis:
                    scores = qa_analysis['Scores']
                    if isinstance(scores, dict) and 'Overall Score' in scores:
                        overall_score_data = scores['Overall Score']
                        if isinstance(overall_score_data, dict) and 'Overall Score' in overall_score_data:
                            overall_score_str = str(overall_score_data['Overall Score'])
                            # Extract numeric value from percentage string
                            if '%' in overall_score_str:
                                overall_score_numeric = float(overall_score_str.replace('%', ''))
                            else:
                                overall_score_numeric = float(overall_score_str)

                            logger.info(f"✅ Extracted overall score: {overall_score_numeric}% for {call_id}")
                        else:
                            logger.warning(f"⚠️ Overall Score structure unexpected for {call_id}: {overall_score_data}")
                    else:
                        logger.warning(f"⚠️ Scores structure unexpected for {call_id}: {scores}")
                else:
                    logger.warning(f"⚠️ No Scores field found for {call_id}")

            except (ValueError, TypeError, KeyError) as e:
                logger.error(f"❌ Error extracting score for {call_id}: {e}")
                overall_score_numeric = 0

            qa_analysis['overall_score_numeric'] = overall_score_numeric
            
            return qa_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing call {call_id}: {e}")
            return {
                'call_id': call_id,
                'folder_id': folder_id,
                'error': str(e),
                'analyzed_at': datetime.now().isoformat(),
                'analysis_method': 'failed'
            }
    
    def store_qa_analysis(self, qa_analysis: Dict[str, Any]) -> bool:
        """Store QA analysis results in both MongoDB and autoqa-qa-results index"""

        try:
            call_id = qa_analysis.get('call_id')

            # Store in MongoDB (qa_analysis collection)
            qa_collection = self.db_connections.get_mongodb_collection('qa_analysis')

            # Upsert the analysis
            qa_collection.update_one(
                {'call_id': call_id},
                {'$set': qa_analysis},
                upsert=True
            )

            # Update transcripts index with qa_status = 'processed'
            transcripts_index = self.db_connections.get_pinecone_index(self.transcripts_index_name)

            # Find the vector for this call_id in transcripts index
            query_result = transcripts_index.query(
                vector=[0.0] * 1536,
                filter={'call_id': call_id},
                top_k=1,
                include_metadata=True
            )

            if query_result.matches:
                vector_id = query_result.matches[0].id
                existing_metadata = query_result.matches[0].metadata

                # Update transcripts index with processing status
                updated_metadata = existing_metadata.copy()
                updated_metadata.update({
                    'qa_status': 'processed',
                    'qa_analyzed_at': qa_analysis.get('analyzed_at'),
                    'qa_score': qa_analysis.get('overall_score_numeric', 0),
                    'qa_method': qa_analysis.get('analysis_method', 'unknown')
                })

                # Update transcripts index
                transcripts_index.update(
                    id=vector_id,
                    set_metadata=updated_metadata
                )

                # Store QA results in qa-results index
                try:
                    qa_results_index = self.db_connections.get_pinecone_index(self.qa_results_index_name)

                    # Generate embedding for QA results (using call summary + key insights)
                    qa_text_content = self.extract_qa_text_for_embedding(qa_analysis)

                    response = self.openai_client.embeddings.create(
                        model="text-embedding-ada-002",
                        input=qa_text_content
                    )
                    qa_embedding = response.data[0].embedding

                    # Prepare QA results metadata
                    qa_metadata = self.prepare_qa_results_metadata(qa_analysis, existing_metadata)

                    # Store in qa-results index
                    qa_results_index.upsert(vectors=[{
                        'id': f"qa-{call_id}",
                        'values': qa_embedding,
                        'metadata': qa_metadata
                    }])

                    logger.info(f"✅ Stored QA results in {self.qa_results_index_name} for {call_id}")

                except Exception as qa_index_error:
                    logger.warning(f"⚠️ Failed to store in qa-results index for {call_id}: {qa_index_error}")
                    # Continue even if qa-results index fails

            logger.info(f"✅ Stored QA analysis for call_id: {call_id}")
            return True

        except Exception as e:
            logger.error(f"Error storing QA analysis: {e}")
            return False

    def extract_qa_text_for_embedding(self, qa_analysis: Dict[str, Any]) -> str:
        """Extract text content from QA analysis for embedding generation"""

        text_parts = []

        # Core QA fields
        core_fields = ['CSAT', 'Call Verdict', 'Issue Resolved', 'Call Topic']
        for field in core_fields:
            if field in qa_analysis and qa_analysis[field]:
                text_parts.append(f"{field}: {qa_analysis[field]}")

        # Call Summary
        if 'Call Summary' in qa_analysis:
            call_summary = qa_analysis['Call Summary']
            if isinstance(call_summary, dict):
                for key, value in call_summary.items():
                    if isinstance(value, list):
                        text_parts.append(f"{key}: {', '.join(value)}")
                    elif isinstance(value, str):
                        text_parts.append(f"{key}: {value}")

        # Assessment Table insights
        if 'Assessment Table' in qa_analysis and isinstance(qa_analysis['Assessment Table'], list):
            failed_items = []
            for item in qa_analysis['Assessment Table']:
                if isinstance(item, dict) and item.get('Achieved Yes/No') == 'No':
                    attribute = item.get('Attribute', 'Unknown')
                    reason = item.get('Reasons', 'No reason provided')
                    failed_items.append(f"{attribute}: {reason}")

            if failed_items:
                text_parts.append(f"Failed QA Items: {'; '.join(failed_items)}")

        return '\n'.join(text_parts)

    def prepare_qa_results_metadata(self, qa_analysis: Dict[str, Any], original_metadata: Dict) -> Dict:
        """Prepare metadata for qa-results index"""

        metadata = {
            'call_id': qa_analysis.get('call_id'),
            'folder_id': qa_analysis.get('folder_id'),
            'analyzed_at': qa_analysis.get('analyzed_at'),
            'analysis_method': qa_analysis.get('analysis_method'),

            # Core QA Results
            'csat': qa_analysis.get('CSAT', 'Unknown'),
            'issue_resolved': qa_analysis.get('Issue Resolved', 'Unknown'),
            'call_verdict': qa_analysis.get('Call Verdict', 'Unknown'),
            'call_topic': qa_analysis.get('Call Topic', 'Unknown'),
            'overall_score_percentage': qa_analysis.get('overall_score_numeric', 0),

            # Original call metadata
            'original_call_datetime': original_metadata.get('datetime', ''),
            'original_duration': original_metadata.get('duration', ''),
            'original_agent_emotion': original_metadata.get('agent_emotion', ''),
            'original_client_emotion': original_metadata.get('client_emotion', ''),

            # QA specific
            'qa_status': 'completed',
            'source': 'autoqa_parallel_processing'
        }

        # Add call summary fields
        if 'Call Summary' in qa_analysis:
            call_summary = qa_analysis['Call Summary']
            if isinstance(call_summary, dict):
                metadata['overall_customer_sentiment'] = call_summary.get('Overall Customer Sentiment', 'Unknown')
                metadata['predicted_customer_satisfaction'] = call_summary.get('Predicted Customer Satisfaction', 'Unknown')

        # Add scoring data
        if 'Scores' in qa_analysis:
            scores = qa_analysis['Scores']
            if isinstance(scores, dict) and 'Overall Score' in scores:
                overall_score = scores['Overall Score']
                if isinstance(overall_score, dict):
                    metadata['achieved_points'] = overall_score.get('Achieved Points', 0)
                    metadata['possible_points'] = overall_score.get('Possible Points', 0)

        return metadata

    def process_single_call(self, vector_info: Dict) -> Dict[str, Any]:
        """Process a single call for QA analysis"""
        
        call_id = vector_info['call_id']
        folder_id = vector_info.get('folder_id', 'unknown')
        
        logger.info(f"🔄 Processing call: {call_id}")
        
        start_time = time.time()
        
        try:
            # Get full transcript
            transcript = self.get_full_transcript_content(call_id)
            if not transcript:
                return {
                    'call_id': call_id,
                    'status': 'failed',
                    'error': 'No transcript content found',
                    'processing_time': time.time() - start_time
                }
            
            # Analyze with OpenAI
            qa_analysis = self.analyze_call_with_openai(call_id, transcript, folder_id)
            if not qa_analysis:
                return {
                    'call_id': call_id,
                    'status': 'failed', 
                    'error': 'OpenAI analysis failed',
                    'processing_time': time.time() - start_time
                }
            
            # Store results
            stored = self.store_qa_analysis(qa_analysis)
            
            processing_time = time.time() - start_time
            
            return {
                'call_id': call_id,
                'status': 'success' if stored else 'partial',
                'qa_score': qa_analysis.get('call_quality_score', 'N/A'),
                'processing_time': processing_time,
                'transcript_length': len(transcript),
                'analysis_method': qa_analysis.get('analysis_method', 'unknown')
            }
            
        except Exception as e:
            return {
                'call_id': call_id,
                'status': 'failed',
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def process_calls_parallel(self, limit: int = None) -> Dict[str, Any]:
        """Process multiple calls in parallel"""
        
        logger.info(f"🚀 Starting parallel QA processing (limit: {limit or 'all'})")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # Get vectors to process
        vectors = self.get_vectors_from_pinecone(limit)
        
        if not vectors:
            logger.warning("No vectors found for processing")
            return {'status': 'no_data', 'processed': 0}
        
        logger.info(f"Processing {len(vectors)} calls with {self.max_workers} workers")
        
        results = []
        successful = 0
        failed = 0
        
        # Process in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_vector = {
                executor.submit(self.process_single_call, vector): vector 
                for vector in vectors
            }
            
            # Collect results
            for future in as_completed(future_to_vector):
                vector = future_to_vector[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result['status'] == 'success':
                        successful += 1
                        logger.info(f"✅ {result['call_id']}: {result['qa_score']} ({result['processing_time']:.1f}s)")
                    else:
                        failed += 1
                        logger.error(f"❌ {result['call_id']}: {result.get('error', 'Unknown error')}")
                        
                except Exception as e:
                    failed += 1
                    logger.error(f"❌ {vector['call_id']}: {e}")
        
        total_time = time.time() - start_time
        
        # Summary
        summary = {
            'status': 'completed',
            'total_calls': len(vectors),
            'successful': successful,
            'failed': failed,
            'success_rate': (successful / len(vectors)) * 100 if vectors else 0,
            'total_time': total_time,
            'avg_time_per_call': total_time / len(vectors) if vectors else 0,
            'calls_per_minute': (len(vectors) / total_time) * 60 if total_time > 0 else 0,
            'results': results
        }
        
        logger.info("=" * 60)
        logger.info("📊 Parallel Processing Summary:")
        logger.info(f"  Total calls: {summary['total_calls']}")
        logger.info(f"  Successful: {summary['successful']}")
        logger.info(f"  Failed: {summary['failed']}")
        logger.info(f"  Success rate: {summary['success_rate']:.1f}%")
        logger.info(f"  Total time: {summary['total_time']:.1f}s")
        logger.info(f"  Avg time per call: {summary['avg_time_per_call']:.1f}s")
        logger.info(f"  Processing rate: {summary['calls_per_minute']:.1f} calls/min")
        
        return summary

def main():
    parser = argparse.ArgumentParser(description="AutoQA Parallel Processing")
    parser.add_argument('--limit', type=int, help='Limit number of calls to process')
    parser.add_argument('--workers', type=int, default=3, help='Number of parallel workers')
    
    args = parser.parse_args()
    
    try:
        processor = AutoQAParallelProcessor(max_workers=args.workers)
        
        print(f"\n🚀 AutoQA Parallel Processing Configuration:")
        print(f"  Limit: {args.limit or 'No limit'}")
        print(f"  Workers: {args.workers}")
        print(f"  Analysis Method: GPT-4o-mini with Comprehensive QA Prompt")
        
        confirm = input(f"\nProceed with parallel processing? (y/N): ").strip().lower()
        if confirm != 'y':
            print("Processing cancelled.")
            return
        
        summary = processor.process_calls_parallel(limit=args.limit)
        
        if summary['status'] == 'completed':
            print(f"\n🎉 Parallel processing completed!")
            print(f"Success rate: {summary['success_rate']:.1f}%")
        else:
            print(f"\n⚠️ Processing completed with issues.")
            
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
