#!/usr/bin/env python3
"""
AutoQA Parallel Processing System
Processes call transcripts using OpenAI Assistant for QA analysis
"""

import sys
import os
import json
import asyncio
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import time

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections
import openai
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutoQAParallelProcessor:
    """Parallel processor for AutoQA analysis"""
    
    def __init__(self, max_workers: int = 3):
        self.db_connections = DatabaseConnections()
        self.openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.max_workers = max_workers
        
        # OpenAI Assistant configuration
        self.assistant_id = os.getenv('OPENAI_ASSISTANT_ID')
        if not self.assistant_id:
            logger.warning("OPENAI_ASSISTANT_ID not set, will use default chat completion")
        
        logger.info(f"✅ AutoQA Parallel Processor initialized with {max_workers} workers")
    
    def get_vectors_from_pinecone(self, limit: int = None) -> List[Dict]:
        """Retrieve vectors from Pinecone for processing"""
        
        logger.info(f"🔍 Retrieving vectors from Pinecone (limit: {limit or 'all'})")
        
        index = self.db_connections.get_pinecone_index()
        
        # Query to get all vectors
        query_result = index.query(
            vector=[0.0] * 1536,  # Dummy vector
            top_k=limit or 10000,  # Get all or limited
            include_metadata=True,
            include_values=False
        )
        
        vectors = []
        for match in query_result.matches:
            if match.metadata and match.metadata.get('autoqa_managed'):
                vectors.append({
                    'vector_id': match.id,
                    'call_id': match.metadata.get('call_id'),
                    'folder_id': match.metadata.get('folder_id'),
                    'transcript_length': match.metadata.get('transcript_length'),
                    'conversation_segments': match.metadata.get('conversation_segments'),
                    'metadata': match.metadata
                })
        
        logger.info(f"✅ Retrieved {len(vectors)} AutoQA-managed vectors")
        return vectors
    
    def get_full_transcript_content(self, call_id: str) -> Optional[str]:
        """Get full transcript content from MongoDB"""
        
        try:
            # Get from primary transcript collection
            collection = self.db_connections.get_mongodb_collection('call_smart_speech_transcribe')
            doc = collection.find_one({'call_id': call_id})
            
            if not doc:
                logger.warning(f"No transcript found for call_id: {call_id}")
                return None
            
            # Extract comprehensive text content
            text_parts = []
            
            # Main transcript
            if 'tran_text' in doc and doc['tran_text']:
                text_parts.append(f"COMPLETE TRANSCRIPT:\n{doc['tran_text']}")
            
            # Diarized transcripts
            diarize_fields = [
                ('agent_diarize_transcript', 'AGENT SIDE'),
                ('client_diarize_transcript', 'CLIENT SIDE'),
                ('speaker_diarize_transcript', 'SPEAKER ANALYSIS')
            ]
            
            for field, label in diarize_fields:
                if field in doc and doc[field]:
                    if isinstance(doc[field], dict) and 'text' in doc[field]:
                        text_parts.append(f"{label}:\n{doc[field]['text']}")
            
            # Conversation flow
            if 'diarize_transcript' in doc and isinstance(doc['diarize_transcript'], list):
                diarize_text = []
                for segment in doc['diarize_transcript']:
                    if isinstance(segment, dict):
                        speaker = segment.get('speaker', 'Unknown')
                        text = segment.get('text', '')
                        diarize_text.append(f"{speaker}: {text}")
                
                if diarize_text:
                    text_parts.append(f"CONVERSATION FLOW:\n" + "\n".join(diarize_text))
            
            return '\n\n'.join(text_parts)
            
        except Exception as e:
            logger.error(f"Error getting transcript for {call_id}: {e}")
            return None
    
    def load_comprehensive_qa_prompt(self) -> str:
        """Load the comprehensive QA prompt from file"""
        try:
            prompt_file = Path(__file__).parent.parent / "QA_Insight_Prompt.txt"
            with open(prompt_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.warning(f"Could not load QA prompt file: {e}")
            return self.get_fallback_prompt()

    def get_fallback_prompt(self) -> str:
        """Fallback prompt if file cannot be loaded"""
        return """
        Please analyze this call transcript for quality assurance following standard QA criteria.
        Provide a comprehensive analysis in JSON format.
        """

    def analyze_call_with_openai(self, call_id: str, transcript: str, folder_id: str) -> Dict[str, Any]:
        """Analyze call transcript using OpenAI Assistant with comprehensive QA prompt"""

        try:
            # Load comprehensive QA prompt
            base_qa_prompt = self.load_comprehensive_qa_prompt()

            # Prepare the complete prompt with transcript
            qa_prompt = f"""
{base_qa_prompt}

Call ID: {call_id}
Folder ID: {folder_id}

TRANSCRIPT TO ANALYZE:
{transcript}

Please provide your analysis following the exact format specified above, ensuring all categories and attributes are assessed according to the detailed criteria provided.
            """
            
            if self.assistant_id:
                # Use OpenAI Assistant
                thread = self.openai_client.beta.threads.create()
                
                message = self.openai_client.beta.threads.messages.create(
                    thread_id=thread.id,
                    role="user",
                    content=qa_prompt
                )
                
                run = self.openai_client.beta.threads.runs.create(
                    thread_id=thread.id,
                    assistant_id=self.assistant_id
                )
                
                # Wait for completion
                while run.status in ['queued', 'in_progress']:
                    time.sleep(2)
                    run = self.openai_client.beta.threads.runs.retrieve(
                        thread_id=thread.id,
                        run_id=run.id
                    )
                
                if run.status == 'completed':
                    messages = self.openai_client.beta.threads.messages.list(
                        thread_id=thread.id
                    )
                    
                    assistant_response = messages.data[0].content[0].text.value
                else:
                    logger.error(f"Assistant run failed for {call_id}: {run.status}")
                    return None
                    
            else:
                # Use chat completion as fallback
                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[
                        {"role": "system", "content": "You are a call quality analysis expert. Analyze call transcripts and provide detailed QA insights."},
                        {"role": "user", "content": qa_prompt}
                    ],
                    max_tokens=2000,
                    temperature=0.3
                )
                
                assistant_response = response.choices[0].message.content
            
            # Try to parse JSON response
            try:
                # Extract JSON from response if it's wrapped in markdown
                if '```json' in assistant_response:
                    json_start = assistant_response.find('```json') + 7
                    json_end = assistant_response.find('```', json_start)
                    json_content = assistant_response[json_start:json_end].strip()
                else:
                    json_content = assistant_response

                qa_analysis = json.loads(json_content)

                # Ensure we have the comprehensive structure
                if not isinstance(qa_analysis, dict):
                    raise json.JSONDecodeError("Response is not a valid JSON object", "", 0)

                # Validate key comprehensive fields
                required_fields = ['CSAT', 'Call Summary', 'Assessment Table', 'Scores']
                missing_fields = [field for field in required_fields if field not in qa_analysis]

                if missing_fields:
                    logger.warning(f"Missing comprehensive QA fields: {missing_fields}")

            except json.JSONDecodeError as e:
                logger.warning(f"JSON parsing failed for {call_id}: {e}")
                # If JSON parsing fails, create structured response with raw content
                qa_analysis = {
                    "CSAT": "Analysis Error",
                    "Issue Resolved": "Unknown",
                    "Call Verdict": "Analysis Error",
                    "Overall Customer Sentiment": "Unknown",
                    "Predicted Customer Satisfaction": "Unknown",
                    "Call Summary": {
                        "Customer Insights": ["Analysis parsing failed"],
                        "Agent Actions": ["Analysis parsing failed"],
                        "Overall Customer Sentiment": "Unknown",
                        "Predicted Customer Satisfaction": "Unknown",
                        "Feedback Summary for the Agent": {
                            "Strengths": ["Analysis parsing failed"],
                            "Areas for Improvement": ["Analysis parsing failed"]
                        }
                    },
                    "Assessment Table": [],
                    "Scores": {
                        "Overall Score": {
                            "Overall Score": "0%"
                        }
                    },
                    "analysis_summary": assistant_response,
                    "raw_response": assistant_response,
                    "parsing_error": str(e)
                }
            
            # Add metadata
            qa_analysis.update({
                'call_id': call_id,
                'folder_id': folder_id,
                'analyzed_at': datetime.now().isoformat(),
                'analysis_method': 'openai_assistant' if self.assistant_id else 'chat_completion',
                'transcript_length': len(transcript)
            })

            # Extract overall score for easy access
            if 'Scores' in qa_analysis and 'Overall Score' in qa_analysis['Scores']:
                overall_score_str = qa_analysis['Scores']['Overall Score'].get('Overall Score', '0%')
                # Extract numeric value from percentage string
                try:
                    if '%' in str(overall_score_str):
                        qa_analysis['overall_score_numeric'] = float(str(overall_score_str).replace('%', ''))
                    else:
                        qa_analysis['overall_score_numeric'] = float(overall_score_str)
                except (ValueError, TypeError):
                    qa_analysis['overall_score_numeric'] = 0
            else:
                qa_analysis['overall_score_numeric'] = 0
            
            return qa_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing call {call_id}: {e}")
            return {
                'call_id': call_id,
                'folder_id': folder_id,
                'error': str(e),
                'analyzed_at': datetime.now().isoformat(),
                'analysis_method': 'failed'
            }
    
    def store_qa_analysis(self, qa_analysis: Dict[str, Any]) -> bool:
        """Store QA analysis results"""
        
        try:
            call_id = qa_analysis.get('call_id')
            
            # Store in MongoDB (qa_analysis collection)
            qa_collection = self.db_connections.get_mongodb_collection('qa_analysis')
            
            # Upsert the analysis
            qa_collection.update_one(
                {'call_id': call_id},
                {'$set': qa_analysis},
                upsert=True
            )
            
            # Also update Pinecone metadata with summary
            index = self.db_connections.get_pinecone_index()
            
            # Find the vector for this call_id
            query_result = index.query(
                vector=[0.0] * 1536,
                filter={'call_id': call_id},
                top_k=1,
                include_metadata=True
            )
            
            if query_result.matches:
                vector_id = query_result.matches[0].id
                existing_metadata = query_result.matches[0].metadata
                
                # Add QA summary to metadata
                updated_metadata = existing_metadata.copy()
                updated_metadata.update({
                    'qa_analyzed': True,
                    'qa_analyzed_at': qa_analysis.get('analyzed_at'),
                    'qa_score': qa_analysis.get('call_quality_score', 'N/A'),
                    'qa_method': qa_analysis.get('analysis_method', 'unknown')
                })
                
                # Update Pinecone
                index.update(
                    id=vector_id,
                    set_metadata=updated_metadata
                )
            
            logger.info(f"✅ Stored QA analysis for call_id: {call_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing QA analysis: {e}")
            return False
    
    def process_single_call(self, vector_info: Dict) -> Dict[str, Any]:
        """Process a single call for QA analysis"""
        
        call_id = vector_info['call_id']
        folder_id = vector_info.get('folder_id', 'unknown')
        
        logger.info(f"🔄 Processing call: {call_id}")
        
        start_time = time.time()
        
        try:
            # Get full transcript
            transcript = self.get_full_transcript_content(call_id)
            if not transcript:
                return {
                    'call_id': call_id,
                    'status': 'failed',
                    'error': 'No transcript content found',
                    'processing_time': time.time() - start_time
                }
            
            # Analyze with OpenAI
            qa_analysis = self.analyze_call_with_openai(call_id, transcript, folder_id)
            if not qa_analysis:
                return {
                    'call_id': call_id,
                    'status': 'failed', 
                    'error': 'OpenAI analysis failed',
                    'processing_time': time.time() - start_time
                }
            
            # Store results
            stored = self.store_qa_analysis(qa_analysis)
            
            processing_time = time.time() - start_time
            
            return {
                'call_id': call_id,
                'status': 'success' if stored else 'partial',
                'qa_score': qa_analysis.get('call_quality_score', 'N/A'),
                'processing_time': processing_time,
                'transcript_length': len(transcript),
                'analysis_method': qa_analysis.get('analysis_method', 'unknown')
            }
            
        except Exception as e:
            return {
                'call_id': call_id,
                'status': 'failed',
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def process_calls_parallel(self, limit: int = None) -> Dict[str, Any]:
        """Process multiple calls in parallel"""
        
        logger.info(f"🚀 Starting parallel QA processing (limit: {limit or 'all'})")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # Get vectors to process
        vectors = self.get_vectors_from_pinecone(limit)
        
        if not vectors:
            logger.warning("No vectors found for processing")
            return {'status': 'no_data', 'processed': 0}
        
        logger.info(f"Processing {len(vectors)} calls with {self.max_workers} workers")
        
        results = []
        successful = 0
        failed = 0
        
        # Process in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_vector = {
                executor.submit(self.process_single_call, vector): vector 
                for vector in vectors
            }
            
            # Collect results
            for future in as_completed(future_to_vector):
                vector = future_to_vector[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result['status'] == 'success':
                        successful += 1
                        logger.info(f"✅ {result['call_id']}: {result['qa_score']} ({result['processing_time']:.1f}s)")
                    else:
                        failed += 1
                        logger.error(f"❌ {result['call_id']}: {result.get('error', 'Unknown error')}")
                        
                except Exception as e:
                    failed += 1
                    logger.error(f"❌ {vector['call_id']}: {e}")
        
        total_time = time.time() - start_time
        
        # Summary
        summary = {
            'status': 'completed',
            'total_calls': len(vectors),
            'successful': successful,
            'failed': failed,
            'success_rate': (successful / len(vectors)) * 100 if vectors else 0,
            'total_time': total_time,
            'avg_time_per_call': total_time / len(vectors) if vectors else 0,
            'calls_per_minute': (len(vectors) / total_time) * 60 if total_time > 0 else 0,
            'results': results
        }
        
        logger.info("=" * 60)
        logger.info("📊 Parallel Processing Summary:")
        logger.info(f"  Total calls: {summary['total_calls']}")
        logger.info(f"  Successful: {summary['successful']}")
        logger.info(f"  Failed: {summary['failed']}")
        logger.info(f"  Success rate: {summary['success_rate']:.1f}%")
        logger.info(f"  Total time: {summary['total_time']:.1f}s")
        logger.info(f"  Avg time per call: {summary['avg_time_per_call']:.1f}s")
        logger.info(f"  Processing rate: {summary['calls_per_minute']:.1f} calls/min")
        
        return summary

def main():
    parser = argparse.ArgumentParser(description="AutoQA Parallel Processing")
    parser.add_argument('--limit', type=int, help='Limit number of calls to process')
    parser.add_argument('--workers', type=int, default=3, help='Number of parallel workers')
    
    args = parser.parse_args()
    
    try:
        processor = AutoQAParallelProcessor(max_workers=args.workers)
        
        print(f"\n🚀 AutoQA Parallel Processing Configuration:")
        print(f"  Limit: {args.limit or 'No limit'}")
        print(f"  Workers: {args.workers}")
        print(f"  OpenAI Assistant: {'Yes' if processor.assistant_id else 'No (using chat completion)'}")
        
        confirm = input(f"\nProceed with parallel processing? (y/N): ").strip().lower()
        if confirm != 'y':
            print("Processing cancelled.")
            return
        
        summary = processor.process_calls_parallel(limit=args.limit)
        
        if summary['status'] == 'completed':
            print(f"\n🎉 Parallel processing completed!")
            print(f"Success rate: {summary['success_rate']:.1f}%")
        else:
            print(f"\n⚠️ Processing completed with issues.")
            
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
