#!/usr/bin/env python3
"""
Debug MySQL Storage
Check what data is actually being stored in MySQL and why fields are NULL
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def check_mysql_data_detailed():
    """Check MySQL data in extreme detail"""
    
    print("🔍 MySQL Data Detailed Analysis")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        
        # Get all records
        cursor.execute('SELECT * FROM qa_results ORDER BY processed_at DESC LIMIT 3')
        results = cursor.fetchall()
        
        print(f"Found {len(results)} records in MySQL")
        
        if not results:
            print("❌ No data in MySQL")
            cursor.close()
            db.close_connections()
            return False
        
        # Check each record in extreme detail
        for i, record in enumerate(results, 1):
            print(f"\n{'='*50}")
            print(f"📋 RECORD {i}: {record['call_id']}")
            print(f"{'='*50}")
            
            # Check ALL fields
            for field_name, value in record.items():
                if value is None:
                    print(f"❌ {field_name}: NULL")
                elif isinstance(value, str) and len(value) > 100:
                    print(f"✅ {field_name}: {len(value)} chars")
                    # Try to parse JSON fields
                    if field_name.endswith('_json'):
                        try:
                            parsed = json.loads(value)
                            if isinstance(parsed, dict):
                                print(f"    JSON keys: {list(parsed.keys())}")
                            elif isinstance(parsed, list):
                                print(f"    JSON list with {len(parsed)} items")
                        except json.JSONDecodeError:
                            print(f"    ❌ INVALID JSON")
                else:
                    print(f"✅ {field_name}: {value}")
            
            # Special check for the problematic fields
            problematic_fields = ['call_date', 'call_duration', 'agent_name', 'customer_name', 'call_type', 
                                'mongodb_id', 'pinecone_id', 'overall_score', 'greeting_score', 
                                'professionalism_score', 'problem_resolution_score', 'closing_score']
            
            null_count = sum(1 for field in problematic_fields if record[field] is None)
            print(f"\n📊 NULL Fields Summary: {null_count}/{len(problematic_fields)} fields are NULL")
            
            # Check if the important JSON fields are populated
            json_fields = ['full_qa_result_json', 'assessment_table_json', 'agent_feedback_json', 'call_summary_json', 'scores_json']
            json_status = {}
            for field in json_fields:
                if record[field] is not None:
                    json_status[field] = f"✅ {len(record[field])} chars"
                else:
                    json_status[field] = "❌ NULL"
            
            print(f"\n📋 JSON Fields Status:")
            for field, status in json_status.items():
                print(f"  {field}: {status}")
        
        cursor.close()
        db.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ Error checking MySQL data: {e}")
        return False

def check_pinecone_qa_data_structure():
    """Check the structure of QA data in Pinecone"""
    
    print("\n🔍 Pinecone QA Data Structure Analysis")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        
        # Check autoqa-qa-results index
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        query_result = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=1,
            include_metadata=True
        )
        
        if query_result.matches:
            metadata = query_result.matches[0].metadata
            call_id = metadata.get('call_id')
            
            print(f"📊 Pinecone Metadata for {call_id}:")
            print("-" * 40)
            
            # Check all metadata fields
            for key, value in metadata.items():
                if isinstance(value, str) and len(value) > 100:
                    print(f"✅ {key}: {len(value)} chars")
                    
                    # Try to parse JSON fields
                    if key.endswith('_json'):
                        try:
                            parsed = json.loads(value)
                            if isinstance(parsed, dict):
                                print(f"    JSON keys: {list(parsed.keys())}")
                            elif isinstance(parsed, list):
                                print(f"    JSON list with {len(parsed)} items")
                        except json.JSONDecodeError:
                            print(f"    ❌ INVALID JSON")
                else:
                    print(f"✅ {key}: {value}")
            
            # Check if full_qa_result_json contains the expected sections
            if 'full_qa_result_json' in metadata:
                try:
                    qa_result = json.loads(metadata['full_qa_result_json'])
                    print(f"\n📋 QA Result Structure:")
                    print(f"  Total keys: {len(qa_result)}")
                    
                    expected_sections = ['Call Summary', 'Scores', 'Assessment Table']
                    for section in expected_sections:
                        if section in qa_result:
                            print(f"  ✅ {section}: Available")
                            if section == 'Call Summary' and isinstance(qa_result[section], dict):
                                print(f"      Keys: {list(qa_result[section].keys())}")
                            elif section == 'Scores' and isinstance(qa_result[section], dict):
                                print(f"      Keys: {list(qa_result[section].keys())}")
                            elif section == 'Assessment Table' and isinstance(qa_result[section], list):
                                print(f"      Items: {len(qa_result[section])}")
                        else:
                            print(f"  ❌ {section}: Missing")
                            
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse full_qa_result_json: {e}")
        
        db.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ Error checking Pinecone data: {e}")
        return False

def compare_expected_vs_actual():
    """Compare what we expect vs what we actually have"""
    
    print("\n🔍 Expected vs Actual Data Comparison")
    print("=" * 60)
    
    # Expected fields that should NOT be NULL
    expected_populated_fields = [
        'call_id', 'folder_id', 'csat', 'issue_resolved', 'call_verdict', 'call_topic',
        'overall_score_percentage', 'analyzed_at', 'analysis_method', 'openai_model',
        'full_qa_result_json', 'assessment_table_json', 'agent_feedback_json', 
        'call_summary_json', 'scores_json', 'status'
    ]
    
    # Fields that are expected to be NULL (not available in source data)
    expected_null_fields = [
        'mongodb_id', 'pinecone_id', 'call_date', 'call_duration', 'agent_name', 
        'customer_name', 'call_type', 'overall_score', 'greeting_score', 
        'professionalism_score', 'problem_resolution_score', 'closing_score',
        'strengths', 'areas_for_improvement', 'specific_feedback', 'compliance_issues',
        'processing_time_seconds', 'openai_tokens_used', 'error_message'
    ]
    
    try:
        db = DatabaseConnections()
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        
        cursor.execute('SELECT * FROM qa_results LIMIT 1')
        record = cursor.fetchone()
        
        if record:
            print("📊 Field Status Analysis:")
            print("-" * 40)
            
            # Check expected populated fields
            print("✅ SHOULD BE POPULATED:")
            for field in expected_populated_fields:
                if field in record:
                    status = "✅ OK" if record[field] is not None else "❌ NULL"
                    print(f"  {field}: {status}")
                else:
                    print(f"  {field}: ❌ FIELD MISSING")
            
            print("\n⚪ EXPECTED TO BE NULL:")
            for field in expected_null_fields:
                if field in record:
                    status = "✅ NULL" if record[field] is None else f"⚠️ HAS VALUE: {record[field]}"
                    print(f"  {field}: {status}")
                else:
                    print(f"  {field}: ❌ FIELD MISSING")
        
        cursor.close()
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Error in comparison: {e}")

if __name__ == "__main__":
    print("🚀 MySQL Storage Debug Analysis")
    print("=" * 70)
    
    # Check MySQL data
    mysql_success = check_mysql_data_detailed()
    
    # Check Pinecone data structure
    pinecone_success = check_pinecone_qa_data_structure()
    
    # Compare expected vs actual
    compare_expected_vs_actual()
    
    print("\n" + "=" * 70)
    print("📊 DEBUG ANALYSIS SUMMARY:")
    print(f"  MySQL Data Check: {'✅ COMPLETED' if mysql_success else '❌ FAILED'}")
    print(f"  Pinecone Data Check: {'✅ COMPLETED' if pinecone_success else '❌ FAILED'}")
    
    print("\n💡 RECOMMENDATIONS:")
    print("1. Check if parallel processor is actually calling store_qa_in_mysql")
    print("2. Verify that QA analysis contains the expected sections")
    print("3. Check if MySQL INSERT query is working correctly")
    print("4. Verify web console is handling NULL fields properly")
