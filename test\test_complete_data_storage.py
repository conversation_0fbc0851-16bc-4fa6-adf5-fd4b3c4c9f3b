#!/usr/bin/env python3
"""
Test Complete Data Storage in AutoQA Pinecone Indexes
Verify that both transcript data and complete QA analysis are properly stored
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def test_complete_data_storage():
    """Test that complete data is stored in both AutoQA indexes"""
    
    print("🧪 Testing Complete Data Storage in AutoQA Indexes")
    print("=" * 70)
    
    db = DatabaseConnections()
    
    # Test autoqa-transcripts index
    print(f"\n📋 TESTING autoqa-transcripts INDEX:")
    print("-" * 50)
    
    try:
        transcripts_index = db.get_pinecone_index('autoqa-transcripts')
        
        # Get sample record
        query_result = transcripts_index.query(
            vector=[0.0] * 1536,
            top_k=1,
            include_metadata=True,
            filter={'autoqa_managed': True}
        )
        
        if query_result.matches:
            metadata = query_result.matches[0].metadata
            call_id = metadata.get('call_id', 'Unknown')
            
            print(f"✅ Sample Call ID: {call_id}")
            
            # Test critical transcript fields
            transcript_tests = [
                ('tran_text', 'Complete transcript text'),
                ('agent_diarize_transcript', 'Agent diarized transcript'),
                ('client_diarize_transcript', 'Client diarized transcript'),
                ('speaker_diarize_transcript', 'Speaker diarized transcript')
            ]
            
            all_transcript_tests_passed = True
            
            for field, description in transcript_tests:
                if field in metadata:
                    value = metadata[field]
                    if isinstance(value, str) and len(value) > 50:
                        print(f"  ✅ {description}: PRESENT ({len(value)} chars)")
                    else:
                        print(f"  ⚠️ {description}: TOO SHORT ({len(value)} chars)")
                        all_transcript_tests_passed = False
                else:
                    print(f"  ❌ {description}: MISSING")
                    all_transcript_tests_passed = False
            
            if all_transcript_tests_passed:
                print(f"  🎉 ALL TRANSCRIPT DATA TESTS PASSED")
            else:
                print(f"  ❌ SOME TRANSCRIPT DATA TESTS FAILED")
                
        else:
            print("❌ No records found in autoqa-transcripts")
            
    except Exception as e:
        print(f"❌ Error testing autoqa-transcripts: {e}")
    
    # Test autoqa-qa-results index
    print(f"\n🎯 TESTING autoqa-qa-results INDEX:")
    print("-" * 50)
    
    try:
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        
        # Get sample record
        query_result = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=1,
            include_metadata=True
        )
        
        if query_result.matches:
            metadata = query_result.matches[0].metadata
            call_id = metadata.get('call_id', 'Unknown')
            
            print(f"✅ Sample Call ID: {call_id}")
            
            # Test critical QA fields
            qa_tests = [
                ('full_qa_result_json', 'Complete QA analysis JSON'),
                ('assessment_table_json', 'Assessment table JSON'),
                ('agent_feedback_json', 'Agent feedback JSON'),
                ('call_summary_json', 'Call summary JSON'),
                ('scores_json', 'Scores JSON')
            ]
            
            all_qa_tests_passed = True
            
            for field, description in qa_tests:
                if field in metadata:
                    value = metadata[field]
                    if isinstance(value, str) and len(value) > 10:
                        try:
                            # Test if it's valid JSON
                            parsed = json.loads(value)
                            if isinstance(parsed, (dict, list)) and len(str(parsed)) > 10:
                                print(f"  ✅ {description}: VALID JSON ({len(value)} chars)")
                            else:
                                print(f"  ⚠️ {description}: JSON TOO SMALL")
                                all_qa_tests_passed = False
                        except json.JSONDecodeError:
                            print(f"  ❌ {description}: INVALID JSON")
                            all_qa_tests_passed = False
                    else:
                        print(f"  ⚠️ {description}: TOO SHORT ({len(value) if value else 0} chars)")
                        all_qa_tests_passed = False
                else:
                    print(f"  ⚠️ {description}: MISSING (optional)")
            
            # Test that full_qa_result_json contains comprehensive data
            if 'full_qa_result_json' in metadata:
                try:
                    qa_json = json.loads(metadata['full_qa_result_json'])
                    
                    # Check for key QA components
                    required_components = [
                        'Assessment Table',
                        'Call Summary', 
                        'Scores',
                        'CSAT',
                        'Call Verdict',
                        'Issue Resolved'
                    ]
                    
                    missing_components = []
                    for component in required_components:
                        if component not in qa_json:
                            missing_components.append(component)
                    
                    if not missing_components:
                        print(f"  ✅ Complete QA JSON contains all required components")
                    else:
                        print(f"  ⚠️ Missing QA components: {missing_components}")
                        all_qa_tests_passed = False
                    
                    # Check Assessment Table structure
                    if 'Assessment Table' in qa_json and isinstance(qa_json['Assessment Table'], list):
                        assessment_count = len(qa_json['Assessment Table'])
                        print(f"  ✅ Assessment Table has {assessment_count} criteria")
                    else:
                        print(f"  ⚠️ Assessment Table structure invalid")
                        all_qa_tests_passed = False
                        
                except json.JSONDecodeError:
                    print(f"  ❌ Cannot parse full_qa_result_json")
                    all_qa_tests_passed = False
            
            if all_qa_tests_passed:
                print(f"  🎉 ALL QA DATA TESTS PASSED")
            else:
                print(f"  ❌ SOME QA DATA TESTS FAILED")
                
        else:
            print("❌ No records found in autoqa-qa-results")
            
    except Exception as e:
        print(f"❌ Error testing autoqa-qa-results: {e}")
    
    # Test data consistency between indexes
    print(f"\n🔗 TESTING DATA CONSISTENCY:")
    print("-" * 50)
    
    try:
        # Get call IDs from both indexes
        transcripts_index = db.get_pinecone_index('autoqa-transcripts')
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        
        # Get transcript call IDs
        transcript_query = transcripts_index.query(
            vector=[0.0] * 1536,
            top_k=100,
            include_metadata=True,
            filter={'autoqa_managed': True}
        )
        
        transcript_call_ids = set()
        for match in transcript_query.matches:
            call_id = match.metadata.get('call_id')
            if call_id:
                transcript_call_ids.add(call_id)
        
        # Get QA results call IDs
        qa_query = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=100,
            include_metadata=True
        )
        
        qa_call_ids = set()
        for match in qa_query.matches:
            call_id = match.metadata.get('call_id')
            if call_id:
                qa_call_ids.add(call_id)
        
        # Check consistency
        common_call_ids = transcript_call_ids.intersection(qa_call_ids)
        transcript_only = transcript_call_ids - qa_call_ids
        qa_only = qa_call_ids - transcript_call_ids
        
        print(f"  📊 Transcript records: {len(transcript_call_ids)}")
        print(f"  📊 QA result records: {len(qa_call_ids)}")
        print(f"  📊 Common call IDs: {len(common_call_ids)}")
        
        if transcript_only:
            print(f"  ⚠️ Transcripts without QA: {len(transcript_only)}")
        
        if qa_only:
            print(f"  ⚠️ QA results without transcripts: {len(qa_only)}")
        
        if len(common_call_ids) == len(transcript_call_ids) == len(qa_call_ids):
            print(f"  ✅ PERFECT DATA CONSISTENCY")
        else:
            print(f"  ⚠️ Data consistency issues detected")
            
    except Exception as e:
        print(f"❌ Error testing data consistency: {e}")
    
    db.close_connections()
    
    print(f"\n🏁 COMPLETE DATA STORAGE TEST FINISHED")
    print("=" * 70)

if __name__ == "__main__":
    test_complete_data_storage()
