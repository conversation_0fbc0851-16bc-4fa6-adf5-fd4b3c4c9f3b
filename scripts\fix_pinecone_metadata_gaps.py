#!/usr/bin/env python3
"""
Fix Critical Pinecone Metadata Gaps for AutoQA Indexes
- Add missing transcript data to autoqa-transcripts index
- Add missing complete QA JSON data to autoqa-qa-results index
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from tqdm import tqdm

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections
import openai
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PineconeMetadataFixer:
    """Fix missing metadata in AutoQA Pinecone indexes"""
    
    def __init__(self):
        self.db_connections = DatabaseConnections()
        
        # Initialize OpenAI client
        self.openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Test OpenAI connection
        try:
            test_response = self.openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input="test"
            )
            logger.info("✅ OpenAI connection successful")
        except Exception as e:
            logger.error(f"❌ OpenAI connection failed: {e}")
            raise
    
    def fix_transcripts_index_metadata(self, limit: int = None, dry_run: bool = False):
        """Fix missing transcript data in autoqa-transcripts index"""
        
        logger.info("🔧 Fixing autoqa-transcripts index metadata...")
        logger.info("=" * 60)
        
        try:
            # Get transcripts index
            transcripts_index = self.db_connections.get_pinecone_index('autoqa-transcripts')
            
            # Query all AutoQA managed vectors
            query_result = transcripts_index.query(
                vector=[0.0] * 1536,
                top_k=10000,  # Get all records
                include_metadata=True,
                filter={'autoqa_managed': True}
            )
            
            total_vectors = len(query_result.matches)
            if limit:
                total_vectors = min(total_vectors, limit)
            
            logger.info(f"Found {total_vectors} vectors to check/fix")
            
            fixed_count = 0
            skipped_count = 0
            error_count = 0
            
            with tqdm(total=total_vectors, desc="Fixing transcript metadata") as pbar:
                for i, match in enumerate(query_result.matches[:total_vectors]):
                    try:
                        vector_id = match.id
                        metadata = match.metadata
                        call_id = metadata.get('call_id')
                        
                        if not call_id:
                            logger.warning(f"No call_id in metadata for vector {vector_id}")
                            error_count += 1
                            pbar.update(1)
                            continue
                        
                        # Check if transcript data is missing
                        missing_fields = []
                        if 'tran_text' not in metadata:
                            missing_fields.append('tran_text')
                        if 'agent_diarize_transcript' not in metadata:
                            missing_fields.append('agent_diarize_transcript')
                        if 'client_diarize_transcript' not in metadata:
                            missing_fields.append('client_diarize_transcript')
                        if 'speaker_diarize_transcript' not in metadata:
                            missing_fields.append('speaker_diarize_transcript')
                        
                        if not missing_fields:
                            skipped_count += 1
                            pbar.update(1)
                            continue
                        
                        # Get transcript data from MongoDB
                        transcript_data = self.get_transcript_data_from_mongodb(call_id)
                        
                        if not transcript_data:
                            logger.warning(f"No transcript data found in MongoDB for {call_id}")
                            error_count += 1
                            pbar.update(1)
                            continue
                        
                        # Prepare updated metadata
                        updated_metadata = metadata.copy()
                        
                        # Add missing transcript fields
                        if 'tran_text' in missing_fields and 'tran_text' in transcript_data:
                            tran_text = str(transcript_data['tran_text'])
                            updated_metadata['tran_text'] = tran_text[:30000] if len(tran_text) > 30000 else tran_text
                        
                        # Add diarized transcript fields
                        diarize_fields = ['agent_diarize_transcript', 'client_diarize_transcript', 'speaker_diarize_transcript']
                        for field in diarize_fields:
                            if field in missing_fields and field in transcript_data:
                                diarize_data = transcript_data[field]
                                if isinstance(diarize_data, dict) and 'text' in diarize_data:
                                    diarize_text = str(diarize_data['text'])
                                    updated_metadata[field] = diarize_text[:10000] if len(diarize_text) > 10000 else diarize_text
                                elif isinstance(diarize_data, str):
                                    diarize_text = str(diarize_data)
                                    updated_metadata[field] = diarize_text[:10000] if len(diarize_text) > 10000 else diarize_text
                        
                        # Update metadata fix timestamp
                        updated_metadata['metadata_fixed_at'] = datetime.now().isoformat()
                        updated_metadata['metadata_fix_version'] = 'v1.0'
                        
                        if not dry_run:
                            # Update vector metadata
                            transcripts_index.update(
                                id=vector_id,
                                set_metadata=updated_metadata
                            )
                        
                        fixed_count += 1
                        logger.info(f"✅ Fixed metadata for {call_id} (added: {missing_fields})")
                        
                    except Exception as e:
                        logger.error(f"❌ Error fixing metadata for vector {vector_id}: {e}")
                        error_count += 1
                    
                    pbar.update(1)
            
            # Summary
            logger.info("=" * 60)
            logger.info("📊 Transcripts Index Fix Summary:")
            logger.info(f"  Total vectors checked: {total_vectors}")
            logger.info(f"  Fixed: {fixed_count}")
            logger.info(f"  Skipped (already complete): {skipped_count}")
            logger.info(f"  Errors: {error_count}")
            
            if dry_run:
                logger.info("🧪 DRY RUN - No changes were made")
            else:
                logger.info("✅ Transcripts index metadata fix completed!")
            
            return fixed_count > 0
            
        except Exception as e:
            logger.error(f"❌ Failed to fix transcripts index metadata: {e}")
            return False
    
    def get_transcript_data_from_mongodb(self, call_id: str) -> Optional[Dict]:
        """Get transcript data from MongoDB for a call_id"""
        try:
            # Get from primary transcript collection
            collection = self.db_connections.get_mongodb_collection('call_smart_speech_transcribe')
            doc = collection.find_one({'call_id': call_id})
            
            if doc:
                return doc
            
            # Try other collections if not found
            other_collections = ['call_smart_speech_details', 'call_smart_speech_details_2']
            for coll_name in other_collections:
                try:
                    collection = self.db_connections.get_mongodb_collection(coll_name)
                    doc = collection.find_one({'call_id': call_id})
                    if doc:
                        return doc
                except:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting transcript data for {call_id}: {e}")
            return None
    
    def fix_qa_results_index_metadata(self, limit: int = None, dry_run: bool = False):
        """Fix missing complete QA JSON data in autoqa-qa-results index"""
        
        logger.info("🔧 Fixing autoqa-qa-results index metadata...")
        logger.info("=" * 60)
        
        try:
            # Get qa-results index
            qa_results_index = self.db_connections.get_pinecone_index('autoqa-qa-results')
            
            # Query all vectors
            query_result = qa_results_index.query(
                vector=[0.0] * 1536,
                top_k=10000,  # Get all records
                include_metadata=True
            )
            
            total_vectors = len(query_result.matches)
            if limit:
                total_vectors = min(total_vectors, limit)
            
            logger.info(f"Found {total_vectors} QA result vectors to check/fix")
            
            fixed_count = 0
            skipped_count = 0
            error_count = 0
            
            with tqdm(total=total_vectors, desc="Fixing QA results metadata") as pbar:
                for i, match in enumerate(query_result.matches[:total_vectors]):
                    try:
                        vector_id = match.id
                        metadata = match.metadata
                        call_id = metadata.get('call_id')
                        
                        if not call_id:
                            logger.warning(f"No call_id in metadata for vector {vector_id}")
                            error_count += 1
                            pbar.update(1)
                            continue
                        
                        # Check if complete QA JSON data is missing
                        missing_fields = []
                        if 'full_qa_result_json' not in metadata:
                            missing_fields.append('full_qa_result_json')
                        if 'assessment_table_json' not in metadata:
                            missing_fields.append('assessment_table_json')
                        if 'agent_feedback_json' not in metadata:
                            missing_fields.append('agent_feedback_json')
                        
                        if not missing_fields:
                            skipped_count += 1
                            pbar.update(1)
                            continue
                        
                        # Get QA analysis from MongoDB
                        qa_data = self.get_qa_analysis_from_mongodb(call_id)
                        
                        if not qa_data:
                            logger.warning(f"No QA analysis found in MongoDB for {call_id}")
                            error_count += 1
                            pbar.update(1)
                            continue
                        
                        # Prepare updated metadata
                        updated_metadata = metadata.copy()
                        
                        # Add missing QA JSON fields
                        try:
                            if 'full_qa_result_json' in missing_fields:
                                updated_metadata['full_qa_result_json'] = json.dumps(qa_data, ensure_ascii=False)
                            
                            if 'assessment_table_json' in missing_fields and 'Assessment Table' in qa_data:
                                updated_metadata['assessment_table_json'] = json.dumps(qa_data['Assessment Table'], ensure_ascii=False)
                            
                            if 'agent_feedback_json' in missing_fields and 'Call Summary' in qa_data:
                                call_summary = qa_data['Call Summary']
                                if isinstance(call_summary, dict) and 'Feedback Summary for the Agent' in call_summary:
                                    updated_metadata['agent_feedback_json'] = json.dumps(call_summary['Feedback Summary for the Agent'], ensure_ascii=False)
                            
                            # Update metadata fix timestamp
                            updated_metadata['metadata_fixed_at'] = datetime.now().isoformat()
                            updated_metadata['metadata_fix_version'] = 'v1.0'
                            
                            if not dry_run:
                                # Update vector metadata
                                qa_results_index.update(
                                    id=vector_id,
                                    set_metadata=updated_metadata
                                )
                            
                            fixed_count += 1
                            logger.info(f"✅ Fixed QA metadata for {call_id} (added: {missing_fields})")
                            
                        except Exception as json_error:
                            logger.error(f"❌ JSON serialization error for {call_id}: {json_error}")
                            error_count += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Error fixing QA metadata for vector {vector_id}: {e}")
                        error_count += 1
                    
                    pbar.update(1)
            
            # Summary
            logger.info("=" * 60)
            logger.info("📊 QA Results Index Fix Summary:")
            logger.info(f"  Total vectors checked: {total_vectors}")
            logger.info(f"  Fixed: {fixed_count}")
            logger.info(f"  Skipped (already complete): {skipped_count}")
            logger.info(f"  Errors: {error_count}")
            
            if dry_run:
                logger.info("🧪 DRY RUN - No changes were made")
            else:
                logger.info("✅ QA results index metadata fix completed!")
            
            return fixed_count > 0
            
        except Exception as e:
            logger.error(f"❌ Failed to fix QA results index metadata: {e}")
            return False
    
    def get_qa_analysis_from_mongodb(self, call_id: str) -> Optional[Dict]:
        """Get QA analysis data from MongoDB for a call_id"""
        try:
            # Get from qa_analysis collection
            collection = self.db_connections.get_mongodb_collection('qa_analysis')
            doc = collection.find_one({'call_id': call_id})
            
            if doc:
                # Remove MongoDB-specific fields
                qa_data = doc.copy()
                qa_data.pop('_id', None)
                return qa_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting QA analysis for {call_id}: {e}")
            return None

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Fix Critical Pinecone Metadata Gaps")
    parser.add_argument('--fix-transcripts', action='store_true', help='Fix autoqa-transcripts index')
    parser.add_argument('--fix-qa-results', action='store_true', help='Fix autoqa-qa-results index')
    parser.add_argument('--fix-all', action='store_true', help='Fix both indexes')
    parser.add_argument('--limit', type=int, help='Limit number of vectors to process')
    parser.add_argument('--dry-run', action='store_true', help='Run without making changes')
    
    args = parser.parse_args()
    
    if not any([args.fix_transcripts, args.fix_qa_results, args.fix_all]):
        print("❌ Please specify what to fix: --fix-transcripts, --fix-qa-results, or --fix-all")
        return
    
    try:
        fixer = PineconeMetadataFixer()
        
        print(f"\n🔧 Pinecone Metadata Fix Configuration:")
        print(f"  Fix Transcripts: {args.fix_transcripts or args.fix_all}")
        print(f"  Fix QA Results: {args.fix_qa_results or args.fix_all}")
        print(f"  Limit: {args.limit or 'No limit'}")
        print(f"  Dry Run: {'Yes' if args.dry_run else 'No'}")
        
        if not args.dry_run:
            confirm = input(f"\nProceed with metadata fixes? (y/N): ").strip().lower()
            if confirm != 'y':
                print("Fix cancelled.")
                return
        
        success = True
        
        if args.fix_transcripts or args.fix_all:
            success &= fixer.fix_transcripts_index_metadata(
                limit=args.limit,
                dry_run=args.dry_run
            )
        
        if args.fix_qa_results or args.fix_all:
            success &= fixer.fix_qa_results_index_metadata(
                limit=args.limit,
                dry_run=args.dry_run
            )
        
        if success:
            print(f"\n🎉 Metadata fixes completed successfully!")
        else:
            print(f"\n❌ Metadata fixes completed with errors.")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Metadata fix failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
