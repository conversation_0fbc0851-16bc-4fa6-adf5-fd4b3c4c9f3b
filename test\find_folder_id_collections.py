#!/usr/bin/env python3
"""
Find Collections with folder_id
Search across all MongoDB collections to find folder_id and map to call_id
"""

import sys
import os
from pathlib import Path
import json
from typing import Dict, List, Any

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def find_folder_id_collections():
    """Search all collections for folder_id field"""
    
    print("🔍 Searching for folder_id across all MongoDB collections")
    print("=" * 70)
    
    try:
        db_connections = DatabaseConnections()
        db = db_connections.get_mongodb_database()
        
        # Get all collection names
        all_collections = db.list_collection_names()
        print(f"Found {len(all_collections)} collections to search:")
        
        for collection_name in all_collections:
            print(f"  - {collection_name}")
        
        print(f"\n🔍 SEARCHING FOR folder_id FIELD:")
        print("-" * 50)
        
        collections_with_folder_id = []
        
        for collection_name in all_collections:
            try:
                collection = db[collection_name]
                
                # Check if any documents have folder_id
                folder_id_count = collection.count_documents({"folder_id": {"$exists": True}})
                total_docs = collection.count_documents({})
                
                print(f"\n{collection_name}:")
                print(f"  Total documents: {total_docs:,}")
                print(f"  Documents with folder_id: {folder_id_count:,}")
                
                if folder_id_count > 0:
                    collections_with_folder_id.append({
                        'collection': collection_name,
                        'total_docs': total_docs,
                        'folder_id_docs': folder_id_count,
                        'percentage': (folder_id_count / total_docs) * 100
                    })
                    
                    # Get sample document with folder_id
                    sample_doc = collection.find_one({"folder_id": {"$exists": True}})
                    if sample_doc:
                        print(f"  ✅ Sample folder_id: {sample_doc.get('folder_id')}")
                        print(f"  ✅ Sample call_id: {sample_doc.get('call_id', 'N/A')}")
                        
                        # Show all fields in this collection
                        fields = list(sample_doc.keys())
                        print(f"  Available fields: {fields}")
                
            except Exception as e:
                print(f"  ❌ Error checking {collection_name}: {e}")
        
        print(f"\n📊 SUMMARY - Collections with folder_id:")
        print("=" * 50)
        
        if collections_with_folder_id:
            for info in collections_with_folder_id:
                print(f"✅ {info['collection']}:")
                print(f"   - {info['folder_id_docs']:,}/{info['total_docs']:,} docs ({info['percentage']:.1f}%)")
        else:
            print("❌ No collections found with folder_id field")
        
        # If we found collections with folder_id, check for call_id overlap
        if collections_with_folder_id:
            print(f"\n🔄 CHECKING CALL_ID OVERLAP:")
            print("-" * 50)
            
            # Get sample call_ids from our main collection
            main_collection = db['call_smart_speech_transcribe']
            sample_call_ids = []
            
            for doc in main_collection.find().limit(10):
                if 'call_id' in doc:
                    sample_call_ids.append(doc['call_id'])
            
            print(f"Sample call_ids from main collection: {sample_call_ids[:5]}...")
            
            # Check each collection with folder_id for matching call_ids
            for info in collections_with_folder_id:
                collection = db[info['collection']]
                
                print(f"\nChecking {info['collection']} for matching call_ids:")
                
                matches = 0
                sample_matches = []
                
                for call_id in sample_call_ids:
                    doc = collection.find_one({"call_id": call_id})
                    if doc:
                        matches += 1
                        if len(sample_matches) < 3:
                            sample_matches.append({
                                'call_id': call_id,
                                'folder_id': doc.get('folder_id', 'N/A')
                            })
                
                print(f"  Matches found: {matches}/{len(sample_call_ids)}")
                
                if sample_matches:
                    print(f"  Sample matches:")
                    for match in sample_matches:
                        print(f"    call_id: {match['call_id']} → folder_id: {match['folder_id']}")
                
                if matches > 0:
                    print(f"  ✅ This collection can be used for cross-lookup!")
                else:
                    print(f"  ❌ No matching call_ids found")
        
        db_connections.close_connections()
        return collections_with_folder_id
        
    except Exception as e:
        print(f"❌ Search failed: {e}")
        return []

def create_cross_lookup_mapping(source_collection: str, lookup_collection: str):
    """Create a mapping between call_id and folder_id"""
    
    print(f"\n🗺️  CREATING CROSS-LOOKUP MAPPING:")
    print(f"Source: {source_collection} → Lookup: {lookup_collection}")
    print("-" * 50)
    
    try:
        db_connections = DatabaseConnections()
        db = db_connections.get_mongodb_database()
        
        source_coll = db[source_collection]
        lookup_coll = db[lookup_collection]
        
        # Get all call_ids from source collection
        source_call_ids = []
        for doc in source_coll.find({}, {"call_id": 1}):
            if 'call_id' in doc:
                source_call_ids.append(doc['call_id'])
        
        print(f"Found {len(source_call_ids)} call_ids in source collection")
        
        # Create mapping
        call_id_to_folder_id = {}
        found_count = 0
        
        for call_id in source_call_ids:
            lookup_doc = lookup_coll.find_one({"call_id": call_id}, {"folder_id": 1})
            if lookup_doc and 'folder_id' in lookup_doc:
                call_id_to_folder_id[call_id] = lookup_doc['folder_id']
                found_count += 1
        
        print(f"Successfully mapped {found_count}/{len(source_call_ids)} call_ids to folder_ids")
        
        if found_count > 0:
            # Show sample mappings
            print(f"\nSample mappings:")
            sample_items = list(call_id_to_folder_id.items())[:5]
            for call_id, folder_id in sample_items:
                print(f"  {call_id} → {folder_id}")
            
            # Save mapping to file
            mapping_file = 'logs/call_id_to_folder_id_mapping.json'
            os.makedirs('logs', exist_ok=True)
            
            with open(mapping_file, 'w') as f:
                json.dump(call_id_to_folder_id, f, indent=2)
            
            print(f"\n✅ Mapping saved to: {mapping_file}")
            print(f"Coverage: {(found_count/len(source_call_ids))*100:.1f}%")
            
            return call_id_to_folder_id
        else:
            print(f"❌ No mappings found")
            return {}
        
        db_connections.close_connections()
        
    except Exception as e:
        print(f"❌ Mapping creation failed: {e}")
        return {}

def suggest_migration_update():
    """Suggest how to update migration script"""
    
    print(f"\n💡 MIGRATION SCRIPT UPDATE SUGGESTIONS:")
    print("=" * 50)
    
    print("""
To add folder_id cross-lookup to your migration script:

1. Load the mapping file at startup:
   ```python
   # In MongoToPineconeMigrator.__init__()
   self.call_id_to_folder_id = {}
   mapping_file = 'logs/call_id_to_folder_id_mapping.json'
   if os.path.exists(mapping_file):
       with open(mapping_file, 'r') as f:
           self.call_id_to_folder_id = json.load(f)
   ```

2. Update prepare_metadata() method:
   ```python
   # In prepare_metadata() method
   if 'call_id' in document:
       call_id = document['call_id']
       metadata['call_id'] = str(call_id)
       
       # Add folder_id from cross-lookup
       if call_id in self.call_id_to_folder_id:
           metadata['folder_id'] = str(self.call_id_to_folder_id[call_id])
   ```

3. Re-run migration with folder_id support:
   ```bash
   python scripts/migrate_mongodb_to_pinecone.py --collection call_smart_speech_transcribe --limit 10
   ```
""")

if __name__ == "__main__":
    print("Starting comprehensive folder_id search...")
    
    collections_with_folder_id = find_folder_id_collections()
    
    if collections_with_folder_id:
        print(f"\n🎉 Found {len(collections_with_folder_id)} collections with folder_id!")
        
        # Ask user which collection to use for cross-lookup
        print(f"\nAvailable collections for cross-lookup:")
        for i, info in enumerate(collections_with_folder_id, 1):
            print(f"  {i}. {info['collection']} ({info['folder_id_docs']:,} docs with folder_id)")
        
        # For automation, use the first one with highest coverage
        best_collection = max(collections_with_folder_id, key=lambda x: x['percentage'])
        
        print(f"\n🎯 Recommended collection: {best_collection['collection']}")
        print(f"   Coverage: {best_collection['percentage']:.1f}%")
        
        # Create mapping
        mapping = create_cross_lookup_mapping(
            'call_smart_speech_transcribe', 
            best_collection['collection']
        )
        
        if mapping:
            suggest_migration_update()
        
    else:
        print(f"\n❌ No collections found with folder_id field.")
        print(f"folder_id may not be part of your data structure.")
    
    print(f"\nSearch complete!")
