#!/usr/bin/env python3
"""
Check for folder_id in MongoDB documents
"""

import sys
import os
from pathlib import Path
import json

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def check_folder_id():
    """Check for folder_id in MongoDB documents"""
    
    print("🔍 Checking for folder_id in MongoDB")
    print("=" * 50)
    
    try:
        db_connections = DatabaseConnections()
        collection = db_connections.get_mongodb_collection('call_smart_speech_transcribe')
        
        # Get sample documents
        sample_docs = list(collection.find().limit(5))
        
        print(f"Checking {len(sample_docs)} sample documents...")
        
        # Check all field names
        all_fields = set()
        for doc in sample_docs:
            all_fields.update(doc.keys())
        
        print(f"\nAll available fields ({len(all_fields)}):")
        for field in sorted(all_fields):
            print(f"  {field}")
        
        # Check for folder-related fields
        folder_fields = [f for f in all_fields if 'folder' in f.lower()]
        id_fields = [f for f in all_fields if 'id' in f.lower() and f != '_id']
        
        print(f"\nFolder-related fields: {folder_fields}")
        print(f"ID-related fields: {id_fields}")
        
        # Check json_data for folder information
        print(f"\nChecking json_data for folder information:")
        
        for i, doc in enumerate(sample_docs[:3]):
            print(f"\nDocument {i+1} (call_id: {doc.get('call_id', 'unknown')}):")
            
            if 'json_data' in doc and doc['json_data']:
                try:
                    json_data = json.loads(doc['json_data'])
                    print(f"  json_data keys: {list(json_data.keys())}")
                    
                    # Look for folder-related keys
                    folder_keys = [k for k in json_data.keys() if 'folder' in k.lower()]
                    if folder_keys:
                        print(f"  Folder keys found: {folder_keys}")
                        for key in folder_keys:
                            print(f"    {key}: {json_data[key]}")
                    else:
                        print(f"  No folder-related keys in json_data")
                        
                except json.JSONDecodeError:
                    print(f"  Could not parse json_data")
            else:
                print(f"  No json_data field")
        
        # Check if folder_id exists in any document
        print(f"\nSearching for folder_id in entire collection...")
        
        folder_id_count = collection.count_documents({"folder_id": {"$exists": True}})
        print(f"Documents with folder_id field: {folder_id_count}")
        
        if folder_id_count > 0:
            sample_with_folder = collection.find_one({"folder_id": {"$exists": True}})
            print(f"Sample folder_id value: {sample_with_folder['folder_id']}")
        
        db_connections.close_connections()
        
        return folder_id_count > 0
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    has_folder_id = check_folder_id()
    
    if has_folder_id:
        print(f"\n✅ folder_id field found in MongoDB!")
    else:
        print(f"\n❌ folder_id field not found in MongoDB")
        print(f"This field may not exist in your data structure.")
