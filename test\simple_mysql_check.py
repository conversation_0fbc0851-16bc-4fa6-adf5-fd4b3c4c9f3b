#!/usr/bin/env python3
"""
Simple MySQL Check
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / ".env", override=True)

from database.connections import DatabaseConnections

db = DatabaseConnections()
mysql_connection = db.connect_mysql()
cursor = mysql_connection.cursor(dictionary=True)

cursor.execute('SELECT call_id, csat, call_verdict, call_summary_json, scores_json, openai_model FROM qa_results LIMIT 3')
results = cursor.fetchall()

print('MySQL Records:')
for record in results:
    print(f'Call ID: {record["call_id"]}')
    print(f'  CSAT: {record["csat"]}')
    print(f'  Verdict: {record["call_verdict"]}')
    print(f'  call_summary_json: {"NULL" if record["call_summary_json"] is None else f"{len(record["call_summary_json"])} chars"}')
    print(f'  scores_json: {"NULL" if record["scores_json"] is None else f"{len(record["scores_json"])} chars"}')
    print(f'  openai_model: {record["openai_model"]}')
    print('-' * 40)

cursor.close()
db.close_connections()
