#!/usr/bin/env python3
"""
MySQL Schema Setup Script for AutoQA System
Creates the necessary database and tables for storing QA results
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from database.connections import DatabaseConnections, MySQLManager
import mysql.connector
from mysql.connector import Error as MySQLError

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MySQLSchemaSetup:
    """Setup MySQL schema for AutoQA system"""
    
    def __init__(self):
        self.db_connections = DatabaseConnections()
        self.mysql_manager = MySQLManager(self.db_connections)
    
    def create_database_if_not_exists(self):
        """Create the AutoQA database if it doesn't exist"""
        try:
            # Connect without specifying database
            config = {
                'host': os.getenv('MYSQL_HOST', 'localhost'),
                'port': int(os.getenv('MYSQL_PORT', 3306)),
                'user': os.getenv('MYSQL_USERNAME', 'root'),
                'password': os.getenv('MYSQL_PASSWORD', ''),
                'autocommit': True
            }
            
            connection = mysql.connector.connect(**config)
            cursor = connection.cursor()
            
            database_name = os.getenv('MYSQL_DATABASE', 'autoqa_results')
            
            # Create database
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{database_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            logger.info(f"✅ Database '{database_name}' created or already exists")
            
            cursor.close()
            connection.close()
            
        except MySQLError as e:
            logger.error(f"❌ Error creating database: {e}")
            raise
    
    def create_tables(self):
        """Create all necessary tables"""
        
        # QA Results table - Enhanced for AutoQA system
        qa_results_table = """
        CREATE TABLE IF NOT EXISTS qa_results (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            call_id VARCHAR(255) NOT NULL UNIQUE,
            mongodb_id VARCHAR(255),
            pinecone_id VARCHAR(255),

            -- Call metadata
            call_date DATETIME,
            call_duration DECIMAL(8,2),
            agent_name VARCHAR(255),
            customer_name VARCHAR(255),
            call_type VARCHAR(100),
            folder_id VARCHAR(255),

            -- AutoQA Core Results (as specified in requirements)
            csat VARCHAR(100),
            issue_resolved VARCHAR(100),
            call_verdict VARCHAR(100),
            call_topic VARCHAR(255),
            overall_score_percentage DECIMAL(5,2),

            -- Legacy QA Analysis Results (for backward compatibility)
            overall_score DECIMAL(5,2),
            greeting_score DECIMAL(5,2),
            professionalism_score DECIMAL(5,2),
            problem_resolution_score DECIMAL(5,2),
            closing_score DECIMAL(5,2),

            -- Detailed Analysis
            strengths TEXT,
            areas_for_improvement TEXT,
            specific_feedback TEXT,
            compliance_issues TEXT,

            -- Complete QA Analysis JSON (as specified in requirements)
            full_qa_result_json LONGTEXT,
            assessment_table_json TEXT,
            agent_feedback_json TEXT,
            call_summary_json TEXT,
            scores_json TEXT,

            -- Processing metadata
            analyzed_at TIMESTAMP NULL,
            analysis_method VARCHAR(255),
            processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processing_time_seconds DECIMAL(8,2),
            openai_model VARCHAR(100),
            openai_tokens_used INT,

            -- Status tracking
            status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            error_message TEXT,
            retry_count INT DEFAULT 0,

            -- Indexes
            INDEX idx_call_id (call_id),
            INDEX idx_status (status),
            INDEX idx_processed_at (processed_at),
            INDEX idx_overall_score (overall_score),
            INDEX idx_overall_score_percentage (overall_score_percentage),
            INDEX idx_csat (csat),
            INDEX idx_call_verdict (call_verdict),
            INDEX idx_folder_id (folder_id),
            INDEX idx_analyzed_at (analyzed_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        # Processing Log table
        processing_log_table = """
        CREATE TABLE IF NOT EXISTS processing_log (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            batch_id VARCHAR(255),
            call_id VARCHAR(255),
            operation VARCHAR(100),
            status ENUM('started', 'completed', 'failed') DEFAULT 'started',
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            end_time TIMESTAMP NULL,
            duration_seconds DECIMAL(8,2),
            error_message TEXT,
            metadata JSON,
            
            INDEX idx_batch_id (batch_id),
            INDEX idx_call_id (call_id),
            INDEX idx_operation (operation),
            INDEX idx_status (status),
            INDEX idx_start_time (start_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        # System Metrics table
        system_metrics_table = """
        CREATE TABLE IF NOT EXISTS system_metrics (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            metric_name VARCHAR(255) NOT NULL,
            metric_value DECIMAL(15,4),
            metric_unit VARCHAR(50),
            recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSON,
            
            INDEX idx_metric_name (metric_name),
            INDEX idx_recorded_at (recorded_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        # Migration Status table
        migration_status_table = """
        CREATE TABLE IF NOT EXISTS migration_status (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            migration_name VARCHAR(255) NOT NULL UNIQUE,
            status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
            started_at TIMESTAMP NULL,
            completed_at TIMESTAMP NULL,
            records_processed INT DEFAULT 0,
            records_total INT DEFAULT 0,
            error_message TEXT,
            metadata JSON,
            
            INDEX idx_migration_name (migration_name),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        tables = [
            ("qa_results", qa_results_table),
            ("processing_log", processing_log_table),
            ("system_metrics", system_metrics_table),
            ("migration_status", migration_status_table)
        ]
        
        for table_name, table_sql in tables:
            try:
                self.mysql_manager.execute_query(table_sql)
                logger.info(f"✅ Table '{table_name}' created or already exists")
            except MySQLError as e:
                logger.error(f"❌ Error creating table '{table_name}': {e}")
                raise
    
    def create_stored_procedures(self):
        """Create useful stored procedures"""
        
        # Procedure to get processing statistics
        stats_procedure = """
        DELIMITER //
        CREATE PROCEDURE IF NOT EXISTS GetProcessingStats()
        BEGIN
            SELECT 
                COUNT(*) as total_calls,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_calls,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_calls,
                COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_calls,
                AVG(overall_score) as avg_score,
                AVG(processing_time_seconds) as avg_processing_time
            FROM qa_results;
        END //
        DELIMITER ;
        """
        
        try:
            self.mysql_manager.execute_query(stats_procedure)
            logger.info("✅ Stored procedures created")
        except MySQLError as e:
            logger.error(f"❌ Error creating stored procedures: {e}")
            # Don't raise here as procedures are optional
    
    def insert_sample_data(self):
        """Insert sample data for testing"""
        
        sample_migration = """
        INSERT IGNORE INTO migration_status (migration_name, status, records_total)
        VALUES ('mongodb_to_pinecone_initial', 'pending', 0)
        """
        
        sample_metrics = """
        INSERT INTO system_metrics (metric_name, metric_value, metric_unit, metadata)
        VALUES 
        ('setup_completed', 1, 'boolean', '{"setup_date": "2024-01-01"}'),
        ('schema_version', 1.0, 'version', '{"created_by": "setup_script"}')
        """
        
        try:
            self.mysql_manager.execute_query(sample_migration)
            self.mysql_manager.execute_query(sample_metrics)
            logger.info("✅ Sample data inserted")
        except MySQLError as e:
            logger.error(f"❌ Error inserting sample data: {e}")
            # Don't raise here as sample data is optional
    
    def verify_setup(self):
        """Verify that all tables were created successfully"""
        
        verify_query = """
        SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = %s
        ORDER BY TABLE_NAME
        """
        
        try:
            database_name = os.getenv('MYSQL_DATABASE', 'autoqa_results')
            results = self.mysql_manager.execute_query(verify_query, (database_name,), fetch=True)
            
            logger.info("📊 Database Schema Verification:")
            logger.info("-" * 60)
            for row in results:
                logger.info(f"Table: {row['TABLE_NAME']:<20} Rows: {row['TABLE_ROWS']:<10} Size: {row['DATA_LENGTH']} bytes")
            
            return len(results) >= 4  # Should have at least 4 tables
            
        except MySQLError as e:
            logger.error(f"❌ Error verifying setup: {e}")
            return False
    
    def run_setup(self):
        """Run the complete setup process"""
        logger.info("🚀 Starting MySQL schema setup for AutoQA system...")
        logger.info("=" * 60)
        
        try:
            # Step 1: Create database
            logger.info("Step 1: Creating database...")
            self.create_database_if_not_exists()
            
            # Step 2: Connect to the database
            logger.info("Step 2: Connecting to database...")
            self.db_connections.connect_mysql()
            
            # Step 3: Create tables
            logger.info("Step 3: Creating tables...")
            self.create_tables()
            
            # Step 4: Create stored procedures
            logger.info("Step 4: Creating stored procedures...")
            self.create_stored_procedures()
            
            # Step 5: Insert sample data
            logger.info("Step 5: Inserting sample data...")
            self.insert_sample_data()
            
            # Step 6: Verify setup
            logger.info("Step 6: Verifying setup...")
            if self.verify_setup():
                logger.info("✅ MySQL schema setup completed successfully!")
                return True
            else:
                logger.error("❌ Schema verification failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            return False
        finally:
            self.db_connections.close_connections()


if __name__ == "__main__":
    setup = MySQLSchemaSetup()
    success = setup.run_setup()
    
    if success:
        print("\n🎉 AutoQA MySQL schema setup completed successfully!")
        print("\nNext steps:")
        print("1. Configure your .env file with database credentials")
        print("2. Run the Pinecone index setup script")
        print("3. Start the data migration process")
    else:
        print("\n❌ Setup failed. Please check the logs and try again.")
        sys.exit(1)
