#!/usr/bin/env python3
"""
Test MySQL Storage Implementation for AutoQA
Test the actual MySQL storage functionality
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def test_mysql_storage():
    """Test MySQL storage functionality"""
    
    print("🧪 Testing MySQL Storage Implementation")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        
        # Test MySQL connection
        print("📋 Testing MySQL Connection...")
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        print("✅ MySQL connection successful")
        
        # Test sample QA data insertion
        print("\n📋 Testing QA Data Insertion...")
        
        sample_qa_data = {
            'call_id': 'test_call_001',
            'folder_id': '1712240879',
            'csat': 'Satisfied',
            'issue_resolved': 'Yes',
            'call_verdict': 'Good',
            'call_topic': 'General Inquiries',
            'overall_score_percentage': 85.5,
            'analyzed_at': datetime.now().isoformat(),
            'analysis_method': 'gpt_4o_mini_comprehensive_prompt',
            'openai_model': 'gpt-4o-mini',
            'full_qa_result_json': json.dumps({
                'CSAT': 'Satisfied',
                'Call Verdict': 'Good',
                'Issue Resolved': 'Yes',
                'Call Topic': 'General Inquiries',
                'Assessment Table': [
                    {
                        'Attribute': 'Agent greeted customer',
                        'Achieved Yes/No': 'Yes',
                        'Points': 1,
                        'Possible': 1,
                        'Reasons': 'Agent provided proper greeting'
                    }
                ],
                'Call Summary': {
                    'Customer Insights': ['Customer was satisfied with service'],
                    'Agent Actions': ['Agent handled call professionally'],
                    'Overall Customer Sentiment': 'Positive',
                    'Predicted Customer Satisfaction': '4/5'
                },
                'Scores': {
                    'Overall Score': {
                        'Achieved Points': 17,
                        'Possible Points': 20,
                        'Overall Score': '85%'
                    }
                }
            }),
            'assessment_table_json': json.dumps([
                {
                    'Attribute': 'Agent greeted customer',
                    'Achieved Yes/No': 'Yes',
                    'Points': 1,
                    'Possible': 1,
                    'Reasons': 'Agent provided proper greeting'
                }
            ]),
            'agent_feedback_json': json.dumps({
                'Strengths': ['Professional greeting', 'Clear communication'],
                'Areas for Improvement': ['Could be more proactive']
            })
        }
        
        # Insert test data
        insert_query = """
        INSERT INTO qa_results (
            call_id, folder_id, csat, issue_resolved, call_verdict, call_topic, 
            overall_score_percentage, analyzed_at, analysis_method, openai_model,
            full_qa_result_json, assessment_table_json, agent_feedback_json, status
        ) VALUES (
            %(call_id)s, %(folder_id)s, %(csat)s, %(issue_resolved)s, %(call_verdict)s, %(call_topic)s,
            %(overall_score_percentage)s, %(analyzed_at)s, %(analysis_method)s, %(openai_model)s,
            %(full_qa_result_json)s, %(assessment_table_json)s, %(agent_feedback_json)s, 'completed'
        ) ON DUPLICATE KEY UPDATE
            csat = VALUES(csat),
            call_verdict = VALUES(call_verdict),
            overall_score_percentage = VALUES(overall_score_percentage),
            full_qa_result_json = VALUES(full_qa_result_json),
            processed_at = CURRENT_TIMESTAMP
        """
        
        cursor.execute(insert_query, sample_qa_data)
        mysql_connection.commit()
        print("✅ Sample QA data inserted successfully")
        
        # Test data retrieval
        print("\n📋 Testing Data Retrieval...")
        
        select_query = """
        SELECT call_id, csat, call_verdict, issue_resolved, call_topic, 
               overall_score_percentage, analyzed_at, analysis_method,
               CHAR_LENGTH(full_qa_result_json) as json_length,
               CHAR_LENGTH(assessment_table_json) as assessment_length,
               CHAR_LENGTH(agent_feedback_json) as feedback_length,
               status, processed_at
        FROM qa_results 
        WHERE call_id = %s
        """
        
        cursor.execute(select_query, (sample_qa_data['call_id'],))
        result = cursor.fetchone()
        
        if result:
            print("✅ Data retrieval successful:")
            print(f"  Call ID: {result['call_id']}")
            print(f"  CSAT: {result['csat']}")
            print(f"  Verdict: {result['call_verdict']}")
            print(f"  Issue Resolved: {result['issue_resolved']}")
            print(f"  Topic: {result['call_topic']}")
            print(f"  Score: {result['overall_score_percentage']}%")
            print(f"  Method: {result['analysis_method']}")
            print(f"  Status: {result['status']}")
            print(f"  JSON Length: {result['json_length']} chars")
            print(f"  Assessment Length: {result['assessment_length']} chars")
            print(f"  Feedback Length: {result['feedback_length']} chars")
        else:
            print("❌ Data retrieval failed")
        
        # Test JSON parsing
        print("\n📋 Testing JSON Data Parsing...")
        
        json_query = """
        SELECT full_qa_result_json, assessment_table_json, agent_feedback_json
        FROM qa_results 
        WHERE call_id = %s
        """
        
        cursor.execute(json_query, (sample_qa_data['call_id'],))
        json_result = cursor.fetchone()
        
        if json_result:
            try:
                # Test full QA result JSON
                if json_result['full_qa_result_json']:
                    full_qa = json.loads(json_result['full_qa_result_json'])
                    print(f"✅ Full QA JSON parsed: {len(full_qa)} keys")
                    print(f"  Contains: {list(full_qa.keys())}")
                
                # Test assessment table JSON
                if json_result['assessment_table_json']:
                    assessment = json.loads(json_result['assessment_table_json'])
                    print(f"✅ Assessment Table JSON parsed: {len(assessment)} items")
                
                # Test agent feedback JSON
                if json_result['agent_feedback_json']:
                    feedback = json.loads(json_result['agent_feedback_json'])
                    print(f"✅ Agent Feedback JSON parsed: {len(feedback)} keys")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
        
        # Test statistics query
        print("\n📋 Testing Statistics Query...")
        
        stats_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN full_qa_result_json IS NOT NULL THEN 1 END) as with_full_json,
            COUNT(CASE WHEN assessment_table_json IS NOT NULL THEN 1 END) as with_assessment,
            COUNT(CASE WHEN agent_feedback_json IS NOT NULL THEN 1 END) as with_feedback,
            AVG(overall_score_percentage) as avg_score,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count
        FROM qa_results
        """
        
        cursor.execute(stats_query)
        stats = cursor.fetchone()
        
        if stats:
            print("✅ Statistics query successful:")
            print(f"  Total Records: {stats['total_records']}")
            print(f"  With Full JSON: {stats['with_full_json']}")
            print(f"  With Assessment: {stats['with_assessment']}")
            print(f"  With Feedback: {stats['with_feedback']}")
            print(f"  Average Score: {stats['avg_score']:.2f}%")
            print(f"  Completed: {stats['completed_count']}")
        
        # Clean up test data
        print("\n📋 Cleaning up test data...")
        delete_query = "DELETE FROM qa_results WHERE call_id = %s"
        cursor.execute(delete_query, (sample_qa_data['call_id'],))
        mysql_connection.commit()
        print("✅ Test data cleaned up")
        
        cursor.close()
        db.close_connections()
        
        print("\n🎉 ALL MYSQL STORAGE TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ MySQL storage test failed: {e}")
        return False

def test_autoqa_parallel_processor_mysql():
    """Test the MySQL storage function from autoqa_parallel_processor"""
    
    print("\n🧪 Testing AutoQA Parallel Processor MySQL Storage")
    print("=" * 60)
    
    try:
        # Import the parallel processor
        sys.path.append(str(project_root / "scripts"))
        from autoqa_parallel_processor import AutoQAParallelProcessor
        
        # Create processor instance
        processor = AutoQAParallelProcessor()
        
        # Create sample QA analysis data
        sample_qa_analysis = {
            'call_id': 'test_processor_001',
            'folder_id': '1712240879',
            'CSAT': 'Satisfied',
            'Issue Resolved': 'Yes',
            'Call Verdict': 'Good',
            'Call Topic': 'General Inquiries',
            'overall_score_numeric': 88.5,
            'analyzed_at': datetime.now().isoformat(),
            'analysis_method': 'gpt_4o_mini_comprehensive_prompt',
            'Assessment Table': [
                {
                    'Attribute': 'Agent greeted customer',
                    'Achieved Yes/No': 'Yes',
                    'Points': 1,
                    'Possible': 1,
                    'Reasons': 'Agent provided proper greeting'
                }
            ],
            'Call Summary': {
                'Customer Insights': ['Customer was satisfied with service'],
                'Agent Actions': ['Agent handled call professionally'],
                'Overall Customer Sentiment': 'Positive',
                'Predicted Customer Satisfaction': '4/5',
                'Feedback Summary for the Agent': {
                    'Strengths': ['Professional greeting'],
                    'Areas for Improvement': ['Could be more proactive']
                }
            },
            'Scores': {
                'Overall Score': {
                    'Achieved Points': 17,
                    'Possible Points': 20,
                    'Overall Score': '88.5%'
                }
            }
        }
        
        # Test the store_qa_in_mysql function
        print("📋 Testing store_qa_in_mysql function...")
        success = processor.store_qa_in_mysql(sample_qa_analysis)
        
        if success:
            print("✅ AutoQA Parallel Processor MySQL storage successful")
            
            # Verify the data was stored
            db = DatabaseConnections()
            mysql_connection = db.connect_mysql()
            cursor = mysql_connection.cursor(dictionary=True)
            
            verify_query = """
            SELECT call_id, csat, call_verdict, overall_score_percentage,
                   CHAR_LENGTH(full_qa_result_json) as json_length
            FROM qa_results 
            WHERE call_id = %s
            """
            
            cursor.execute(verify_query, (sample_qa_analysis['call_id'],))
            result = cursor.fetchone()
            
            if result:
                print("✅ Data verification successful:")
                print(f"  Call ID: {result['call_id']}")
                print(f"  CSAT: {result['csat']}")
                print(f"  Verdict: {result['call_verdict']}")
                print(f"  Score: {result['overall_score_percentage']}%")
                print(f"  JSON Length: {result['json_length']} chars")
            
            # Clean up
            delete_query = "DELETE FROM qa_results WHERE call_id = %s"
            cursor.execute(delete_query, (sample_qa_analysis['call_id'],))
            mysql_connection.commit()
            cursor.close()
            db.close_connections()
            print("✅ Test data cleaned up")
            
        else:
            print("❌ AutoQA Parallel Processor MySQL storage failed")
            return False
        
        print("\n🎉 AUTOQA PARALLEL PROCESSOR MYSQL TEST PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ AutoQA Parallel Processor MySQL test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting MySQL Storage Tests")
    print("=" * 70)
    
    # Test basic MySQL storage
    test1_success = test_mysql_storage()
    
    # Test AutoQA parallel processor MySQL storage
    test2_success = test_autoqa_parallel_processor_mysql()
    
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY:")
    print(f"  Basic MySQL Storage: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    print(f"  AutoQA Processor Storage: {'✅ PASSED' if test2_success else '❌ FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 ALL MYSQL STORAGE TESTS PASSED!")
        print("\n✅ MySQL storage implementation is working correctly!")
        print("✅ AutoQA parallel processor can store QA results in MySQL!")
        print("✅ Complete QA JSON data is being stored and can be retrieved!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        sys.exit(1)
