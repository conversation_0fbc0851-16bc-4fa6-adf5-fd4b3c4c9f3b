#!/usr/bin/env python3
"""
Verify Pinecone Metadata Structure for AutoQA Indexes
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def verify_metadata_structure():
    """Verify metadata structure in both AutoQA indexes"""
    
    print("🔍 Verifying AutoQA Pinecone Metadata Structure")
    print("=" * 70)
    
    db = DatabaseConnections()
    
    # Check autoqa-transcripts index
    print(f"\n📋 CHECKING autoqa-transcripts INDEX:")
    print("-" * 50)
    
    try:
        transcripts_index = db.get_pinecone_index('autoqa-transcripts')
        
        # Get sample record
        query_result = transcripts_index.query(
            vector=[0.0] * 1536,
            top_k=1,
            include_metadata=True,
            filter={'autoqa_managed': True}
        )
        
        if query_result.matches:
            metadata = query_result.matches[0].metadata
            call_id = metadata.get('call_id', 'Unknown')
            
            print(f"Sample Call ID: {call_id}")
            print(f"Total metadata fields: {len(metadata)}")
            
            # Check for critical transcript fields
            critical_transcript_fields = [
                'tran_text', 'agent_diarize_transcript', 'client_diarize_transcript',
                'speaker_diarize_transcript', 'diarize_transcript'
            ]
            
            print(f"\n🔍 Critical Transcript Fields Check:")
            for field in critical_transcript_fields:
                if field in metadata:
                    value = metadata[field]
                    if isinstance(value, str) and len(value) > 100:
                        print(f"  ✅ {field}: Present ({len(value)} chars)")
                    elif isinstance(value, str):
                        print(f"  ⚠️ {field}: Present but short ({len(value)} chars)")
                    else:
                        print(f"  ⚠️ {field}: Present but not string ({type(value)})")
                else:
                    print(f"  ❌ {field}: MISSING")
            
            print(f"\n📝 All metadata fields:")
            for key, value in sorted(metadata.items()):
                if isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {type(value).__name__} ({len(value)} chars)")
                else:
                    print(f"  {key}: {value}")
        else:
            print("❌ No records found in autoqa-transcripts")
            
    except Exception as e:
        print(f"❌ Error checking autoqa-transcripts: {e}")
    
    # Check autoqa-qa-results index
    print(f"\n🎯 CHECKING autoqa-qa-results INDEX:")
    print("-" * 50)
    
    try:
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        
        # Get sample record
        query_result = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=1,
            include_metadata=True
        )
        
        if query_result.matches:
            metadata = query_result.matches[0].metadata
            call_id = metadata.get('call_id', 'Unknown')
            
            print(f"Sample Call ID: {call_id}")
            print(f"Total metadata fields: {len(metadata)}")
            
            # Check for critical QA fields
            critical_qa_fields = [
                'full_qa_result_json', 'assessment_table_json', 'agent_feedback_json',
                'csat', 'call_verdict', 'overall_score_percentage'
            ]
            
            print(f"\n🔍 Critical QA Fields Check:")
            for field in critical_qa_fields:
                if field in metadata:
                    value = metadata[field]
                    if field.endswith('_json') and isinstance(value, str):
                        try:
                            parsed = json.loads(value)
                            print(f"  ✅ {field}: Valid JSON ({len(value)} chars, {len(parsed)} keys)")
                        except json.JSONDecodeError:
                            print(f"  ⚠️ {field}: Invalid JSON ({len(value)} chars)")
                    else:
                        print(f"  ✅ {field}: {value}")
                else:
                    print(f"  ❌ {field}: MISSING")
            
            print(f"\n📝 All metadata fields:")
            for key, value in sorted(metadata.items()):
                if isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {type(value).__name__} ({len(value)} chars)")
                else:
                    print(f"  {key}: {value}")
                    
            # If full_qa_result_json exists, show its structure
            if 'full_qa_result_json' in metadata:
                try:
                    qa_json = json.loads(metadata['full_qa_result_json'])
                    print(f"\n📊 QA JSON Structure:")
                    for key in sorted(qa_json.keys()):
                        print(f"  - {key}")
                except:
                    print(f"\n❌ Could not parse full_qa_result_json")
        else:
            print("❌ No records found in autoqa-qa-results")
            
    except Exception as e:
        print(f"❌ Error checking autoqa-qa-results: {e}")
    
    db.close_connections()

if __name__ == "__main__":
    verify_metadata_structure()
