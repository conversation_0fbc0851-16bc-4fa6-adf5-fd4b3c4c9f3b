#!/usr/bin/env python3
"""
Examine QA Data in MongoDB
Since we have 3 QA documents, let's see what they contain
"""

import sys
from pathlib import Path
import json

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def examine_qa_data():
    """Examine the QA data we have in MongoDB"""
    
    print("📊 Examining QA Data in MongoDB")
    print("=" * 50)
    
    try:
        db = DatabaseConnections()
        qa_collection = db.get_mongodb_collection('qa_analysis')
        
        # Get all QA documents
        qa_docs = list(qa_collection.find().sort('analyzed_at', -1))
        
        print(f"Total QA documents: {len(qa_docs)}")
        
        for i, doc in enumerate(qa_docs, 1):
            print(f"\n📋 QA Document {i}:")
            print(f"  Call ID: {doc.get('call_id')}")
            print(f"  Method: {doc.get('analysis_method')}")
            print(f"  Analyzed: {doc.get('analyzed_at')}")
            
            # Check comprehensive QA fields
            print(f"  CSAT: {doc.get('CSAT', 'MISSING')}")
            print(f"  Call Verdict: {doc.get('Call Verdict', 'MISSING')}")
            print(f"  Overall Score Numeric: {doc.get('overall_score_numeric', 'MISSING')}")
            
            # Check Scores structure
            if 'Scores' in doc:
                scores = doc['Scores']
                print(f"  Scores Type: {type(scores)}")
                if isinstance(scores, dict) and 'Overall Score' in scores:
                    overall = scores['Overall Score']
                    print(f"  Overall Score Structure: {overall}")
                else:
                    print(f"  Scores Content: {scores}")
            else:
                print(f"  Scores: MISSING")
            
            # Check for parsing errors
            if 'parsing_error' in doc:
                print(f"  ❌ Parsing Error: {doc['parsing_error']}")
            
            # Check raw response
            if 'raw_response' in doc:
                raw = doc['raw_response']
                print(f"  Raw Response Length: {len(raw)} chars")
                
                # Try to find JSON in raw response
                if '{' in raw and '}' in raw:
                    try:
                        # Extract potential JSON
                        start = raw.find('{')
                        end = raw.rfind('}') + 1
                        potential_json = raw[start:end]
                        
                        parsed = json.loads(potential_json)
                        print(f"  ✅ Raw response contains valid JSON")
                        print(f"  JSON Keys: {list(parsed.keys())}")
                        
                        # Check for scores in raw JSON
                        if 'Scores' in parsed:
                            raw_scores = parsed['Scores']
                            print(f"  Raw JSON Scores: {raw_scores}")
                        
                    except json.JSONDecodeError:
                        print(f"  ❌ Raw response JSON parsing failed")
                        print(f"  Raw preview: {raw[:200]}...")
            
            print("-" * 40)
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Examination failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    examine_qa_data()
