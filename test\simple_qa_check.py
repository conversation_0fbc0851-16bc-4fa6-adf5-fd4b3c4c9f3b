#!/usr/bin/env python3
"""
Simple QA Check
"""

import sys
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def main():
    print("Simple QA Check")
    print("=" * 30)
    
    db = DatabaseConnections()
    qa_collection = db.get_mongodb_collection('qa_analysis')
    
    count = qa_collection.count_documents({})
    print(f"QA documents: {count}")
    
    if count > 0:
        doc = qa_collection.find_one()
        print(f"Call ID: {doc.get('call_id', 'N/A')}")
        print(f"CSAT: {doc.get('CSAT', 'N/A')}")
        print(f"Score: {doc.get('overall_score_numeric', 'N/A')}")
        
        if 'raw_response' in doc:
            raw = doc['raw_response']
            print(f"Raw response length: {len(raw)}")
            print(f"Raw preview: {raw[:100]}...")
    
    db.close_connections()

if __name__ == "__main__":
    main()
