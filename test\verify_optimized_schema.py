#!/usr/bin/env python3
"""
Verify Optimized MySQL Schema
Compare before/after and test functionality
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / ".env", override=True)

from database.connections import DatabaseConnections
import requests

def compare_table_structures():
    """Compare original vs optimized table structures"""
    
    print("🔍 MySQL Schema Comparison")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        
        # Get optimized table structure
        cursor.execute("DESCRIBE qa_results")
        optimized_columns = cursor.fetchall()
        
        # Get backup table structure (if exists)
        try:
            cursor.execute("DESCRIBE qa_results_backup")
            original_columns = cursor.fetchall()
            has_backup = True
        except:
            has_backup = False
            original_columns = []
        
        print(f"📊 OPTIMIZED TABLE (qa_results): {len(optimized_columns)} columns")
        print("-" * 40)
        for col in optimized_columns:
            null_status = "NULL" if col['Null'] == 'YES' else "NOT NULL"
            key_status = f" ({col['Key']})" if col['Key'] else ""
            print(f"  ✅ {col['Field']:<30} {col['Type']:<20} {null_status}{key_status}")
        
        if has_backup:
            print(f"\n📊 ORIGINAL TABLE (qa_results_backup): {len(original_columns)} columns")
            print("-" * 40)
            
            # Show removed fields
            optimized_field_names = {col['Field'] for col in optimized_columns}
            original_field_names = {col['Field'] for col in original_columns}
            removed_fields = original_field_names - optimized_field_names
            
            print(f"❌ REMOVED FIELDS ({len(removed_fields)}):")
            for field in sorted(removed_fields):
                original_col = next(col for col in original_columns if col['Field'] == field)
                print(f"  ❌ {field:<30} {original_col['Type']:<20} (was always NULL)")
            
            print(f"\n📈 OPTIMIZATION RESULTS:")
            print(f"  Columns reduced: {len(original_columns)} → {len(optimized_columns)}")
            print(f"  Fields removed: {len(removed_fields)}")
            print(f"  Space saved: ~{len(removed_fields) * 8} bytes per record")
        
        cursor.close()
        db.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ Error comparing schemas: {e}")
        return False

def verify_data_integrity():
    """Verify all data was preserved correctly"""
    
    print("\n🔍 Data Integrity Verification")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        
        # Check record count
        cursor.execute("SELECT COUNT(*) as count FROM qa_results")
        optimized_count = cursor.fetchone()['count']
        
        try:
            cursor.execute("SELECT COUNT(*) as count FROM qa_results_backup")
            original_count = cursor.fetchone()['count']
            print(f"📊 Record Count Comparison:")
            print(f"  Original: {original_count} records")
            print(f"  Optimized: {optimized_count} records")
            print(f"  Status: {'✅ PRESERVED' if original_count == optimized_count else '❌ DATA LOSS'}")
        except:
            print(f"📊 Optimized Table: {optimized_count} records")
        
        # Check critical fields are populated
        cursor.execute("""
            SELECT 
                call_id,
                csat,
                call_verdict,
                issue_resolved,
                call_topic,
                overall_score_percentage,
                CASE WHEN full_qa_result_json IS NOT NULL THEN LENGTH(full_qa_result_json) ELSE 0 END as qa_json_length,
                CASE WHEN assessment_table_json IS NOT NULL THEN LENGTH(assessment_table_json) ELSE 0 END as assessment_length,
                openai_model,
                status
            FROM qa_results
            ORDER BY call_id
        """)
        
        records = cursor.fetchall()
        
        print(f"\n📋 Data Quality Check:")
        print("-" * 40)
        
        for record in records:
            print(f"Call ID: {record['call_id']}")
            print(f"  ✅ CSAT: {record['csat']}")
            print(f"  ✅ Verdict: {record['call_verdict']}")
            print(f"  ✅ Issue Resolved: {record['issue_resolved']}")
            print(f"  ✅ Topic: {record['call_topic']}")
            print(f"  ✅ Score: {record['overall_score_percentage']}%")
            print(f"  ✅ QA JSON: {record['qa_json_length']} chars")
            print(f"  ✅ Assessment: {record['assessment_length']} chars")
            print(f"  ✅ Model: {record['openai_model']}")
            print(f"  ✅ Status: {record['status']}")
            print("-" * 40)
        
        cursor.close()
        db.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying data integrity: {e}")
        return False

def test_web_console_compatibility():
    """Test that web console still works with optimized schema"""
    
    print("\n🌐 Web Console Compatibility Test")
    print("=" * 60)
    
    try:
        # Start web server in background for testing
        import subprocess
        import time
        import signal
        import os
        
        # Start server
        server_process = subprocess.Popen([
            sys.executable, "web/qa_results_server.py"
        ], cwd=Path(__file__).parent.parent, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        time.sleep(3)
        
        try:
            # Test API endpoints
            response = requests.get("http://localhost:5000/api/qa-results", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Web Console API Working")
                print(f"  Success: {data.get('success')}")
                print(f"  AutoQA QA Results: {len(data.get('autoqa_qa_results', []))}")
                print(f"  AutoQA Transcripts: {len(data.get('autoqa_transcripts', []))}")
                
                # Test specific call details
                if data.get('autoqa_qa_results'):
                    sample_call_id = data['autoqa_qa_results'][0]['call_id']
                    detail_response = requests.get(f"http://localhost:5000/api/call/{sample_call_id}", timeout=10)
                    
                    if detail_response.status_code == 200:
                        detail_data = detail_response.json()
                        print(f"✅ Call Details API Working")
                        print(f"  Call ID: {detail_data.get('call_id')}")
                        print(f"  AutoQA Data Found: {bool(detail_data.get('autoqa_qa_data'))}")
                    else:
                        print(f"❌ Call Details API Failed: {detail_response.status_code}")
                
                web_console_success = True
            else:
                print(f"❌ Web Console API Failed: {response.status_code}")
                web_console_success = False
                
        except Exception as e:
            print(f"❌ Web Console Test Error: {e}")
            web_console_success = False
        
        finally:
            # Stop server
            try:
                server_process.terminate()
                server_process.wait(timeout=5)
            except:
                server_process.kill()
        
        return web_console_success
        
    except Exception as e:
        print(f"❌ Error testing web console: {e}")
        return False

def main():
    """Run complete verification"""
    
    print("🚀 Optimized MySQL Schema Verification")
    print("=" * 70)
    
    # Test 1: Compare table structures
    schema_ok = compare_table_structures()
    
    # Test 2: Verify data integrity
    data_ok = verify_data_integrity()
    
    # Test 3: Test web console compatibility
    web_ok = test_web_console_compatibility()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 VERIFICATION SUMMARY:")
    print(f"  Schema Optimization: {'✅ PASSED' if schema_ok else '❌ FAILED'}")
    print(f"  Data Integrity: {'✅ PASSED' if data_ok else '❌ FAILED'}")
    print(f"  Web Console: {'✅ PASSED' if web_ok else '❌ FAILED'}")
    
    if schema_ok and data_ok and web_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ MySQL schema optimization successful!")
        print("✅ No more NULL fields cluttering the database!")
        print("✅ All functionality preserved!")
        print("✅ Cleaner, more maintainable database structure!")
    else:
        print("\n❌ Some tests failed. Please review the results above.")
    
    return schema_ok and data_ok and web_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
