#!/usr/bin/env python3
"""
Update Pinecone Metadata with QA Results
Sync QA analysis from MongoDB to Pinecone metadata
"""

import sys
import os
from pathlib import Path
import argparse
from datetime import datetime

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def update_pinecone_qa_metadata():
    """Update Pinecone metadata with QA analysis results"""
    
    print("🔄 Updating Pinecone Metadata with QA Results")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        
        # Get QA analysis from MongoDB
        qa_collection = db.get_mongodb_collection('qa_analysis')
        qa_docs = list(qa_collection.find())
        
        print(f"Found {len(qa_docs)} QA analysis documents in MongoDB")
        
        if not qa_docs:
            print("❌ No QA analysis found in MongoDB")
            return
        
        # Get Pinecone index
        index = db.get_pinecone_index()
        
        updated_count = 0
        
        for qa_doc in qa_docs:
            call_id = qa_doc.get('call_id')
            if not call_id:
                print(f"⚠️ Skipping QA doc without call_id")
                continue
            
            print(f"\n🔄 Processing {call_id}...")
            
            # Query Pinecone for this call_id
            query_result = index.query(
                vector=[0.0] * 1536,
                filter={'call_id': call_id},
                top_k=1,
                include_metadata=True
            )
            
            if not query_result.matches:
                print(f"❌ No Pinecone vector found for {call_id}")
                continue
            
            match = query_result.matches[0]
            vector_id = match.id
            existing_metadata = match.metadata
            
            # Prepare QA metadata
            qa_metadata = {
                'qa_analyzed': True,
                'qa_score': qa_doc.get('overall_score_numeric', 0),
                'qa_method': qa_doc.get('analysis_method', 'gpt4o_mini_comprehensive_prompt'),
                'qa_analyzed_at': qa_doc.get('analyzed_at', datetime.now().isoformat()),
                'autoqa_managed': True,  # Mark as AutoQA managed
                'csat': qa_doc.get('CSAT', 'Unknown'),
                'call_verdict': qa_doc.get('Call Verdict', 'Unknown'),
                'issue_resolved': qa_doc.get('Issue Resolved', 'Unknown'),
                'call_topic': qa_doc.get('Call Topic', 'Unknown')
            }
            
            # Merge with existing metadata
            updated_metadata = {**existing_metadata, **qa_metadata}
            
            # Update Pinecone vector metadata
            index.update(
                id=vector_id,
                metadata=updated_metadata
            )
            
            print(f"✅ Updated {call_id}: Score={qa_metadata['qa_score']}%, CSAT={qa_metadata['csat']}")
            updated_count += 1
        
        print(f"\n🎉 Successfully updated {updated_count} Pinecone vectors with QA metadata")
        
        # Verify the updates
        print(f"\n🔍 Verifying updates...")
        autoqa_query = index.query(
            vector=[0.0] * 1536,
            top_k=10,
            include_metadata=True,
            filter={'autoqa_managed': True}
        )
        
        print(f"AutoQA Managed Vectors: {len(autoqa_query.matches)}")
        for i, match in enumerate(autoqa_query.matches, 1):
            metadata = match.metadata
            call_id = metadata.get('call_id', 'Unknown')
            qa_score = metadata.get('qa_score', 'N/A')
            csat = metadata.get('csat', 'N/A')
            print(f"  {i}. {call_id}: Score={qa_score}%, CSAT={csat}")
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Update failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Update Pinecone metadata with QA results')
    parser.add_argument('--confirm', action='store_true', help='Confirm the update operation')
    
    args = parser.parse_args()
    
    if not args.confirm:
        print("⚠️ This will update Pinecone metadata with QA analysis results.")
        print("⚠️ Use --confirm flag to proceed.")
        return
    
    update_pinecone_qa_metadata()

if __name__ == "__main__":
    main()
