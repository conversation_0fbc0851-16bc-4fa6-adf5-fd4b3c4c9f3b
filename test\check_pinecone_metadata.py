#!/usr/bin/env python3
"""
Check Pinecone Metadata Structure
See what fields are actually available in Pinecone
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def check_pinecone_metadata():
    """Check what metadata is actually stored in Pinecone"""
    
    print("🔍 Checking Pinecone Metadata Structure")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        
        # Check autoqa-qa-results index
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        query_result = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=1,
            include_metadata=True
        )
        
        if query_result.matches:
            metadata = query_result.matches[0].metadata
            call_id = metadata.get('call_id')
            
            print(f"📊 Sample Metadata for {call_id}:")
            print("-" * 60)
            
            # Show all metadata keys
            print("Available metadata keys:")
            for key in sorted(metadata.keys()):
                value = metadata[key]
                if isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: [String - {len(value)} chars]")
                else:
                    print(f"  {key}: {value}")
            
            # Check if we have full_qa_result_json and parse it
            if 'full_qa_result_json' in metadata:
                print(f"\n📋 Parsing full_qa_result_json:")
                try:
                    qa_result = json.loads(metadata['full_qa_result_json'])
                    print("Available QA result keys:")
                    for key in sorted(qa_result.keys()):
                        value = qa_result[key]
                        if isinstance(value, dict):
                            print(f"  {key}: [Dict with {len(value)} keys]")
                        elif isinstance(value, list):
                            print(f"  {key}: [List with {len(value)} items]")
                        elif isinstance(value, str) and len(value) > 50:
                            print(f"  {key}: [String - {len(value)} chars]")
                        else:
                            print(f"  {key}: {value}")
                    
                    # Check specific sections we need
                    print(f"\n📋 Checking specific sections:")
                    
                    # Call Summary
                    if 'Call Summary' in qa_result:
                        call_summary = qa_result['Call Summary']
                        print(f"  Call Summary: {type(call_summary)} with keys: {list(call_summary.keys()) if isinstance(call_summary, dict) else 'Not a dict'}")
                    else:
                        print(f"  Call Summary: NOT FOUND")
                    
                    # Scores
                    if 'Scores' in qa_result:
                        scores = qa_result['Scores']
                        print(f"  Scores: {type(scores)} with keys: {list(scores.keys()) if isinstance(scores, dict) else 'Not a dict'}")
                    else:
                        print(f"  Scores: NOT FOUND")
                    
                    # Assessment Table
                    if 'Assessment Table' in qa_result:
                        assessment = qa_result['Assessment Table']
                        print(f"  Assessment Table: {type(assessment)} with {len(assessment) if isinstance(assessment, list) else 'Not a list'} items")
                    else:
                        print(f"  Assessment Table: NOT FOUND")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse full_qa_result_json: {e}")
            else:
                print("❌ No full_qa_result_json found in metadata")
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Error checking Pinecone metadata: {e}")

def extract_missing_fields_from_qa_json():
    """Extract the missing fields from the QA JSON that should be in MySQL"""
    
    print("\n🔧 Extracting Missing Fields from QA JSON")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        
        # Get sample QA data
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        query_result = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=1,
            include_metadata=True
        )
        
        if query_result.matches:
            metadata = query_result.matches[0].metadata
            call_id = metadata.get('call_id')
            
            if 'full_qa_result_json' in metadata:
                qa_result = json.loads(metadata['full_qa_result_json'])
                
                print(f"📊 Extracting fields for {call_id}:")
                
                # Extract Call Summary JSON
                if 'Call Summary' in qa_result:
                    call_summary_json = json.dumps(qa_result['Call Summary'], ensure_ascii=False)
                    print(f"  call_summary_json: {len(call_summary_json)} chars")
                else:
                    print(f"  call_summary_json: NOT AVAILABLE")
                
                # Extract Scores JSON
                if 'Scores' in qa_result:
                    scores_json = json.dumps(qa_result['Scores'], ensure_ascii=False)
                    print(f"  scores_json: {len(scores_json)} chars")
                    
                    # Extract individual scores
                    scores = qa_result['Scores']
                    print(f"  Available score categories:")
                    for category, score_data in scores.items():
                        if isinstance(score_data, dict) and 'Score' in score_data:
                            print(f"    {category}: {score_data['Score']}")
                else:
                    print(f"  scores_json: NOT AVAILABLE")
                
                # Extract OpenAI model info
                openai_model = qa_result.get('analysis_method', 'gpt-4o-mini')
                print(f"  openai_model: {openai_model}")
                
                # Show what we can extract for missing fields
                print(f"\n💡 Fields we can extract:")
                print(f"  - call_summary_json: From 'Call Summary' section")
                print(f"  - scores_json: From 'Scores' section")
                print(f"  - openai_model: From 'analysis_method' or default")
                print(f"  - Individual scores: From 'Scores' breakdown")
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Error extracting fields: {e}")

if __name__ == "__main__":
    print("🚀 Pinecone Metadata Analysis")
    print("=" * 70)
    
    check_pinecone_metadata()
    extract_missing_fields_from_qa_json()
    
    print("\n💡 NEXT STEPS:")
    print("1. Update migration script to extract missing fields from QA JSON")
    print("2. Re-run migration to populate NULL fields")
    print("3. Fix web console to handle remaining NULL values gracefully")
