#!/usr/bin/env python3
"""
Check MySQL Data Status
Simple script to check if data is stored in MySQL
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def check_mysql_data():
    """Check current MySQL data status"""
    
    print("🔍 Checking MySQL Data Status")
    print("=" * 50)
    
    try:
        db = DatabaseConnections()
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        
        # Check current data count
        cursor.execute('SELECT COUNT(*) as count FROM qa_results')
        count_result = cursor.fetchone()
        print(f"Current rows in qa_results: {count_result['count']}")
        
        if count_result['count'] > 0:
            # Show sample data
            cursor.execute('SELECT call_id, csat, call_verdict, overall_score_percentage, status, processed_at FROM qa_results LIMIT 5')
            results = cursor.fetchall()
            
            print("\nSample data:")
            for row in results:
                print(f"  Call ID: {row['call_id']}")
                print(f"  CSAT: {row['csat']}")
                print(f"  Verdict: {row['call_verdict']}")
                print(f"  Score: {row['overall_score_percentage']}%")
                print(f"  Status: {row['status']}")
                print(f"  Processed: {row['processed_at']}")
                print("-" * 30)
        else:
            print("❌ No data found in qa_results table")
            print("\n🔍 Checking table structure...")
            cursor.execute('DESCRIBE qa_results')
            columns = cursor.fetchall()
            print("Table columns:")
            for col in columns:
                print(f"  {col['Field']}: {col['Type']}")
        
        cursor.close()
        db.close_connections()
        
        return count_result['count'] > 0
        
    except Exception as e:
        print(f"❌ Error checking MySQL data: {e}")
        return False

def check_pinecone_qa_data():
    """Check if we have QA data in Pinecone that should be in MySQL"""
    
    print("\n🔍 Checking Pinecone QA Data")
    print("=" * 50)
    
    try:
        db = DatabaseConnections()
        
        # Check autoqa-qa-results index
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        stats = qa_results_index.describe_index_stats()
        print(f"autoqa-qa-results vectors: {stats.total_vector_count}")
        
        if stats.total_vector_count > 0:
            # Get sample QA data
            query_result = qa_results_index.query(
                vector=[0.0] * 1536,
                top_k=3,
                include_metadata=True
            )
            
            print("\nSample QA data in Pinecone:")
            for i, match in enumerate(query_result.matches, 1):
                metadata = match.metadata
                print(f"  {i}. Call ID: {metadata.get('call_id')}")
                print(f"     CSAT: {metadata.get('csat')}")
                print(f"     Verdict: {metadata.get('call_verdict')}")
                print(f"     Score: {metadata.get('overall_score_percentage')}%")
                print(f"     Has full JSON: {bool(metadata.get('full_qa_result_json'))}")
                print("-" * 30)
            
            return True
        else:
            print("❌ No QA data found in Pinecone")
            return False
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Error checking Pinecone QA data: {e}")
        return False

if __name__ == "__main__":
    print("🚀 MySQL Data Status Check")
    print("=" * 60)
    
    mysql_has_data = check_mysql_data()
    pinecone_has_qa_data = check_pinecone_qa_data()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"  MySQL has data: {'✅ YES' if mysql_has_data else '❌ NO'}")
    print(f"  Pinecone has QA data: {'✅ YES' if pinecone_has_qa_data else '❌ NO'}")
    
    if not mysql_has_data and pinecone_has_qa_data:
        print("\n💡 ISSUE IDENTIFIED:")
        print("  - Pinecone has QA data but MySQL is empty")
        print("  - The parallel processor is not storing data in MySQL")
        print("  - Need to fix the MySQL storage in parallel processor")
    elif mysql_has_data:
        print("\n✅ MySQL storage is working correctly!")
    else:
        print("\n⚠️ No QA data found in either system")
