#!/usr/bin/env python3
"""
Update MySQL Schema for AutoQA
Add missing columns to existing qa_results table
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_mysql_schema():
    """Update MySQL schema to add missing columns"""
    
    print("🔧 Updating MySQL Schema for AutoQA")
    print("=" * 50)
    
    try:
        db = DatabaseConnections()
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor()
        
        # List of columns to add
        columns_to_add = [
            "ADD COLUMN IF NOT EXISTS folder_id VARCHAR(255) AFTER pinecone_id",
            "ADD COLUMN IF NOT EXISTS csat VARCHAR(100) AFTER call_type",
            "ADD COLUMN IF NOT EXISTS issue_resolved VARCHAR(100) AFTER csat",
            "ADD COLUMN IF NOT EXISTS call_verdict VARCHAR(100) AFTER issue_resolved",
            "ADD COLUMN IF NOT EXISTS call_topic VARCHAR(255) AFTER call_verdict",
            "ADD COLUMN IF NOT EXISTS overall_score_percentage DECIMAL(5,2) AFTER call_topic",
            "ADD COLUMN IF NOT EXISTS full_qa_result_json LONGTEXT AFTER compliance_issues",
            "ADD COLUMN IF NOT EXISTS assessment_table_json TEXT AFTER full_qa_result_json",
            "ADD COLUMN IF NOT EXISTS agent_feedback_json TEXT AFTER assessment_table_json",
            "ADD COLUMN IF NOT EXISTS call_summary_json TEXT AFTER agent_feedback_json",
            "ADD COLUMN IF NOT EXISTS scores_json TEXT AFTER call_summary_json",
            "ADD COLUMN IF NOT EXISTS analyzed_at TIMESTAMP NULL AFTER scores_json",
            "ADD COLUMN IF NOT EXISTS analysis_method VARCHAR(255) AFTER analyzed_at"
        ]
        
        # Add indexes
        indexes_to_add = [
            "ADD INDEX IF NOT EXISTS idx_overall_score_percentage (overall_score_percentage)",
            "ADD INDEX IF NOT EXISTS idx_csat (csat)",
            "ADD INDEX IF NOT EXISTS idx_call_verdict (call_verdict)",
            "ADD INDEX IF NOT EXISTS idx_folder_id (folder_id)",
            "ADD INDEX IF NOT EXISTS idx_analyzed_at (analyzed_at)"
        ]
        
        print("📋 Adding missing columns...")
        
        for column_def in columns_to_add:
            try:
                alter_query = f"ALTER TABLE qa_results {column_def}"
                cursor.execute(alter_query)
                mysql_connection.commit()
                print(f"✅ {column_def}")
            except Exception as e:
                if "Duplicate column name" in str(e) or "already exists" in str(e):
                    print(f"⏭️ Column already exists: {column_def}")
                else:
                    print(f"❌ Error adding column: {e}")
        
        print("\n📋 Adding missing indexes...")
        
        for index_def in indexes_to_add:
            try:
                alter_query = f"ALTER TABLE qa_results {index_def}"
                cursor.execute(alter_query)
                mysql_connection.commit()
                print(f"✅ {index_def}")
            except Exception as e:
                if "Duplicate key name" in str(e) or "already exists" in str(e):
                    print(f"⏭️ Index already exists: {index_def}")
                else:
                    print(f"❌ Error adding index: {e}")
        
        # Verify the schema
        print("\n📋 Verifying updated schema...")
        
        describe_query = "DESCRIBE qa_results"
        cursor.execute(describe_query)
        columns = cursor.fetchall()
        
        print("✅ Current table structure:")
        for column in columns:
            print(f"  {column[0]}: {column[1]}")
        
        cursor.close()
        db.close_connections()
        
        print("\n🎉 MySQL schema update completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ MySQL schema update failed: {e}")
        return False

if __name__ == "__main__":
    success = update_mysql_schema()
    
    if success:
        print("\n✅ Schema update successful!")
        print("You can now run the MySQL storage tests again.")
    else:
        print("\n❌ Schema update failed!")
        sys.exit(1)
