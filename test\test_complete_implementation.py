#!/usr/bin/env python3
"""
Complete Implementation Test
Test both MySQL storage and Web Console integration
"""

import sys
import os
import json
import requests
import time
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def test_mysql_storage_implementation():
    """Test MySQL storage implementation"""
    
    print("🧪 Testing MySQL Storage Implementation")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        
        # Check data count
        cursor.execute('SELECT COUNT(*) as count FROM qa_results')
        count_result = cursor.fetchone()
        print(f"✅ MySQL records found: {count_result['count']}")
        
        if count_result['count'] > 0:
            # Get sample data with all key fields
            cursor.execute('''
                SELECT call_id, csat, call_verdict, issue_resolved, call_topic, 
                       overall_score_percentage, analysis_method, status,
                       CHAR_LENGTH(full_qa_result_json) as json_length,
                       CHAR_LENGTH(assessment_table_json) as assessment_length,
                       CHAR_LENGTH(agent_feedback_json) as feedback_length
                FROM qa_results 
                LIMIT 3
            ''')
            results = cursor.fetchall()
            
            print("✅ Sample MySQL data:")
            for row in results:
                print(f"  Call ID: {row['call_id']}")
                print(f"    CSAT: {row['csat']}")
                print(f"    Verdict: {row['call_verdict']}")
                print(f"    Issue Resolved: {row['issue_resolved']}")
                print(f"    Topic: {row['call_topic']}")
                print(f"    Score: {row['overall_score_percentage']}%")
                print(f"    Method: {row['analysis_method']}")
                print(f"    Status: {row['status']}")
                print(f"    JSON Length: {row['json_length']} chars")
                print(f"    Assessment Length: {row['assessment_length']} chars")
                print(f"    Feedback Length: {row['feedback_length']} chars")
                print("-" * 40)
            
            # Test JSON parsing
            cursor.execute('SELECT call_id, full_qa_result_json FROM qa_results LIMIT 1')
            json_test = cursor.fetchone()
            
            if json_test and json_test['full_qa_result_json']:
                try:
                    parsed_json = json.loads(json_test['full_qa_result_json'])
                    print(f"✅ JSON parsing successful for {json_test['call_id']}")
                    print(f"    JSON keys: {list(parsed_json.keys())}")
                except json.JSONDecodeError:
                    print(f"❌ JSON parsing failed for {json_test['call_id']}")
                    return False
            
            cursor.close()
            db.close_connections()
            return True
        else:
            print("❌ No data found in MySQL")
            cursor.close()
            db.close_connections()
            return False
            
    except Exception as e:
        print(f"❌ MySQL test failed: {e}")
        return False

def test_web_console_integration():
    """Test web console AutoQA integration"""
    
    print("\n🧪 Testing Web Console AutoQA Integration")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    try:
        # Test health endpoint
        print("📋 Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health endpoint working")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
        
        # Test main QA results API
        print("\n📋 Testing QA results API...")
        response = requests.get(f"{base_url}/api/qa-results", timeout=15)
        if response.status_code == 200:
            data = response.json()
            print("✅ QA results API working")
            print(f"  Success: {data.get('success')}")
            
            summary = data.get('summary', {})
            print(f"  Total QA analyses: {summary.get('total_count', 0)}")
            print(f"  AutoQA transcripts: {summary.get('autoqa_transcripts', 0)}")
            print(f"  AutoQA QA results: {summary.get('autoqa_qa_results', 0)}")
            print(f"  AutoQA avg score: {summary.get('autoqa_avg_score', 0)}%")
            
            # Check AutoQA data
            autoqa_qa_results = data.get('autoqa_qa_results', [])
            autoqa_transcripts = data.get('autoqa_transcripts', [])
            
            if autoqa_qa_results:
                print(f"✅ Found {len(autoqa_qa_results)} AutoQA QA results")
                sample_qa = autoqa_qa_results[0]
                print(f"  Sample QA result:")
                print(f"    Call ID: {sample_qa.get('call_id')}")
                print(f"    CSAT: {sample_qa.get('csat')}")
                print(f"    Verdict: {sample_qa.get('call_verdict')}")
                print(f"    Score: {sample_qa.get('overall_score_percentage')}%")
                print(f"    Has full QA JSON: {sample_qa.get('has_full_qa_json')}")
                print(f"    Has assessment table: {sample_qa.get('has_assessment_table')}")
                print(f"    Has agent feedback: {sample_qa.get('has_agent_feedback')}")
                
                # Test specific call details
                call_id = sample_qa.get('call_id')
                if call_id:
                    print(f"\n📋 Testing call details for {call_id}...")
                    detail_response = requests.get(f"{base_url}/api/call/{call_id}", timeout=10)
                    if detail_response.status_code == 200:
                        detail_data = detail_response.json()
                        print("✅ Call details API working")
                        
                        autoqa_qa_data = detail_data.get('autoqa_qa_data', {})
                        if autoqa_qa_data:
                            print("✅ AutoQA data found in call details")
                            print(f"  CSAT: {autoqa_qa_data.get('csat')}")
                            print(f"  Verdict: {autoqa_qa_data.get('call_verdict')}")
                            print(f"  Score: {autoqa_qa_data.get('overall_score_percentage')}%")
                            
                            # Check parsed JSON data
                            if autoqa_qa_data.get('full_qa_result_parsed'):
                                print("✅ Complete QA JSON data parsed successfully")
                                qa_result = autoqa_qa_data['full_qa_result_parsed']
                                print(f"    QA result keys: {list(qa_result.keys())}")
                            
                            if autoqa_qa_data.get('assessment_table_parsed'):
                                print("✅ Assessment table parsed successfully")
                                assessment = autoqa_qa_data['assessment_table_parsed']
                                print(f"    Assessment items: {len(assessment)}")
                            
                            if autoqa_qa_data.get('agent_feedback_parsed'):
                                print("✅ Agent feedback parsed successfully")
                        else:
                            print("❌ No AutoQA data in call details")
                            return False
                    else:
                        print(f"❌ Call details API failed: {detail_response.status_code}")
                        return False
                
                # Test AutoQA search endpoint
                print(f"\n📋 Testing AutoQA search for {call_id}...")
                search_response = requests.get(f"{base_url}/api/autoqa/search/{call_id}", timeout=10)
                if search_response.status_code == 200:
                    search_data = search_response.json()
                    print("✅ AutoQA search API working")
                    print(f"  Found: {search_data.get('found')}")
                    
                    qa_data = search_data.get('qa_data', {})
                    if qa_data:
                        print("✅ QA data found in search")
                        print(f"    CSAT: {qa_data.get('csat')}")
                        print(f"    Verdict: {qa_data.get('call_verdict')}")
                        print(f"    Score: {qa_data.get('overall_score_percentage')}%")
                        
                        if qa_data.get('full_qa_result'):
                            print("✅ Complete QA analysis available")
                            print(f"    QA keys: {list(qa_data['full_qa_result'].keys())}")
                        
                        if qa_data.get('assessment_table'):
                            print("✅ Assessment table available")
                            print(f"    Assessment items: {len(qa_data['assessment_table'])}")
                    else:
                        print("❌ No QA data in search results")
                        return False
                else:
                    print(f"❌ AutoQA search API failed: {search_response.status_code}")
                    return False
            else:
                print("❌ No AutoQA QA results found")
                return False
            
            if autoqa_transcripts:
                print(f"✅ Found {len(autoqa_transcripts)} AutoQA transcripts")
            else:
                print("⚠️ No AutoQA transcripts found")
            
            return True
        else:
            print(f"❌ QA results API failed: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Web console test failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_data_consistency():
    """Test data consistency between MySQL and Pinecone"""
    
    print("\n🧪 Testing Data Consistency")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        
        # Get MySQL data
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        cursor.execute('SELECT call_id, csat, call_verdict, overall_score_percentage FROM qa_results')
        mysql_data = {row['call_id']: row for row in cursor.fetchall()}
        cursor.close()
        
        # Get Pinecone data
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        query_result = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=10,
            include_metadata=True
        )
        
        pinecone_data = {
            match.metadata.get('call_id'): match.metadata 
            for match in query_result.matches 
            if match.metadata.get('call_id')
        }
        
        db.close_connections()
        
        print(f"✅ MySQL records: {len(mysql_data)}")
        print(f"✅ Pinecone records: {len(pinecone_data)}")
        
        # Check consistency
        consistent = True
        for call_id in mysql_data:
            if call_id in pinecone_data:
                mysql_row = mysql_data[call_id]
                pinecone_row = pinecone_data[call_id]
                
                # Compare key fields
                if (mysql_row['csat'] == pinecone_row.get('csat') and
                    mysql_row['call_verdict'] == pinecone_row.get('call_verdict') and
                    abs(float(mysql_row['overall_score_percentage']) - float(pinecone_row.get('overall_score_percentage', 0))) < 0.01):
                    print(f"✅ {call_id}: Data consistent")
                else:
                    print(f"❌ {call_id}: Data inconsistent")
                    print(f"    MySQL: {mysql_row['csat']}, {mysql_row['call_verdict']}, {mysql_row['overall_score_percentage']}%")
                    print(f"    Pinecone: {pinecone_row.get('csat')}, {pinecone_row.get('call_verdict')}, {pinecone_row.get('overall_score_percentage')}%")
                    consistent = False
            else:
                print(f"❌ {call_id}: Missing in Pinecone")
                consistent = False
        
        return consistent
        
    except Exception as e:
        print(f"❌ Data consistency test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Complete Implementation Test")
    print("=" * 70)
    
    # Test MySQL storage
    mysql_success = test_mysql_storage_implementation()
    
    # Test web console
    web_success = test_web_console_integration()
    
    # Test data consistency
    consistency_success = test_data_consistency()
    
    print("\n" + "=" * 70)
    print("📊 COMPLETE IMPLEMENTATION TEST SUMMARY:")
    print(f"  MySQL Storage: {'✅ PASSED' if mysql_success else '❌ FAILED'}")
    print(f"  Web Console: {'✅ PASSED' if web_success else '❌ FAILED'}")
    print(f"  Data Consistency: {'✅ PASSED' if consistency_success else '❌ FAILED'}")
    
    if mysql_success and web_success and consistency_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ MySQL storage implementation is working correctly!")
        print("✅ Web console displays AutoQA data properly!")
        print("✅ Data is consistent between MySQL and Pinecone!")
        print("✅ Complete QA analysis is accessible through web interface!")
        print("\n🚀 BOTH CRITICAL COMPONENTS ARE FULLY FUNCTIONAL!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        if not mysql_success:
            print("  - MySQL storage needs attention")
        if not web_success:
            print("  - Web console needs attention")
        if not consistency_success:
            print("  - Data consistency needs attention")
        sys.exit(1)
