#!/usr/bin/env python3
"""
Optimize MySQL Schema - Remove Unsupported Fields
Remove fields that are always NULL and optimize the qa_results table structure
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from database.connections import DatabaseConnections
import mysql.connector
from mysql.connector import Error as MySQLError

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MySQLSchemaOptimizer:
    """Optimize MySQL schema by removing unsupported fields"""
    
    def __init__(self):
        self.db_connections = DatabaseConnections()
    
    def backup_existing_data(self):
        """Backup existing data before schema changes"""
        logger.info("📋 Backing up existing data...")
        
        try:
            mysql_connection = self.db_connections.connect_mysql()
            cursor = mysql_connection.cursor(dictionary=True)
            
            # Get all existing data
            cursor.execute("SELECT * FROM qa_results")
            existing_data = cursor.fetchall()
            
            logger.info(f"✅ Found {len(existing_data)} existing records to preserve")
            
            cursor.close()
            return existing_data
            
        except MySQLError as e:
            logger.error(f"❌ Error backing up data: {e}")
            raise
    
    def create_optimized_table(self):
        """Create the new optimized qa_results table"""
        
        # Optimized table with only supported fields
        optimized_table_sql = """
        CREATE TABLE IF NOT EXISTS qa_results_optimized (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            call_id VARCHAR(255) NOT NULL UNIQUE,
            
            -- Source metadata (available from processing)
            folder_id VARCHAR(255),
            
            -- AutoQA Core Results
            csat VARCHAR(100),
            issue_resolved VARCHAR(100),
            call_verdict VARCHAR(100),
            call_topic VARCHAR(255),
            overall_score_percentage DECIMAL(5,2),
            
            -- Processing metadata
            analyzed_at TIMESTAMP NULL,
            processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            analysis_method VARCHAR(255),
            openai_model VARCHAR(100),
            
            -- Complete JSON results for complex queries
            full_qa_result_json LONGTEXT,
            assessment_table_json LONGTEXT,
            agent_feedback_json TEXT,
            call_summary_json LONGTEXT,
            scores_json TEXT,
            
            -- Status tracking
            status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            error_message TEXT,
            
            -- Indexes for performance
            INDEX idx_call_id (call_id),
            INDEX idx_csat (csat),
            INDEX idx_call_verdict (call_verdict),
            INDEX idx_analyzed_at (analyzed_at),
            INDEX idx_status (status),
            INDEX idx_folder_id (folder_id),
            INDEX idx_overall_score_percentage (overall_score_percentage)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        try:
            mysql_connection = self.db_connections.connect_mysql()
            cursor = mysql_connection.cursor()
            
            logger.info("🔧 Creating optimized qa_results table...")
            cursor.execute(optimized_table_sql)
            
            logger.info("✅ Optimized table created successfully")
            cursor.close()
            
        except MySQLError as e:
            logger.error(f"❌ Error creating optimized table: {e}")
            raise
    
    def migrate_data_to_optimized_table(self, backup_data):
        """Migrate existing data to the optimized table"""
        
        if not backup_data:
            logger.info("ℹ️ No existing data to migrate")
            return
        
        logger.info(f"📦 Migrating {len(backup_data)} records to optimized table...")
        
        # Insert query for optimized table (only supported fields)
        insert_sql = """
        INSERT INTO qa_results_optimized (
            call_id, folder_id, csat, issue_resolved, call_verdict, call_topic,
            overall_score_percentage, analyzed_at, analysis_method, openai_model,
            full_qa_result_json, assessment_table_json, agent_feedback_json,
            call_summary_json, scores_json, status, error_message
        ) VALUES (
            %(call_id)s, %(folder_id)s, %(csat)s, %(issue_resolved)s, %(call_verdict)s, %(call_topic)s,
            %(overall_score_percentage)s, %(analyzed_at)s, %(analysis_method)s, %(openai_model)s,
            %(full_qa_result_json)s, %(assessment_table_json)s, %(agent_feedback_json)s,
            %(call_summary_json)s, %(scores_json)s, %(status)s, %(error_message)s
        )
        """
        
        try:
            mysql_connection = self.db_connections.connect_mysql()
            cursor = mysql_connection.cursor()
            
            migrated_count = 0
            for record in backup_data:
                # Extract only supported fields
                optimized_record = {
                    'call_id': record.get('call_id'),
                    'folder_id': record.get('folder_id'),
                    'csat': record.get('csat'),
                    'issue_resolved': record.get('issue_resolved'),
                    'call_verdict': record.get('call_verdict'),
                    'call_topic': record.get('call_topic'),
                    'overall_score_percentage': record.get('overall_score_percentage'),
                    'analyzed_at': record.get('analyzed_at'),
                    'analysis_method': record.get('analysis_method'),
                    'openai_model': record.get('openai_model'),
                    'full_qa_result_json': record.get('full_qa_result_json'),
                    'assessment_table_json': record.get('assessment_table_json'),
                    'agent_feedback_json': record.get('agent_feedback_json'),
                    'call_summary_json': record.get('call_summary_json'),
                    'scores_json': record.get('scores_json'),
                    'status': record.get('status'),
                    'error_message': record.get('error_message')
                }
                
                cursor.execute(insert_sql, optimized_record)
                migrated_count += 1
            
            mysql_connection.commit()
            logger.info(f"✅ Successfully migrated {migrated_count} records")
            cursor.close()
            
        except MySQLError as e:
            logger.error(f"❌ Error migrating data: {e}")
            raise
    
    def replace_original_table(self):
        """Replace the original table with the optimized one"""
        
        try:
            mysql_connection = self.db_connections.connect_mysql()
            cursor = mysql_connection.cursor()
            
            logger.info("🔄 Replacing original table with optimized version...")
            
            # Drop original table and rename optimized table
            cursor.execute("DROP TABLE IF EXISTS qa_results_backup")
            cursor.execute("RENAME TABLE qa_results TO qa_results_backup")
            cursor.execute("RENAME TABLE qa_results_optimized TO qa_results")
            
            logger.info("✅ Table replacement completed")
            logger.info("ℹ️ Original table backed up as 'qa_results_backup'")
            
            cursor.close()
            
        except MySQLError as e:
            logger.error(f"❌ Error replacing table: {e}")
            raise
    
    def verify_optimized_schema(self):
        """Verify the optimized schema"""
        
        try:
            mysql_connection = self.db_connections.connect_mysql()
            cursor = mysql_connection.cursor(dictionary=True)
            
            # Get table structure
            cursor.execute("DESCRIBE qa_results")
            columns = cursor.fetchall()
            
            # Get record count
            cursor.execute("SELECT COUNT(*) as count FROM qa_results")
            count_result = cursor.fetchone()
            
            logger.info("📊 Optimized Schema Verification:")
            logger.info("-" * 60)
            logger.info(f"Total records: {count_result['count']}")
            logger.info(f"Total columns: {len(columns)}")
            logger.info("\nColumn structure:")
            
            for col in columns:
                logger.info(f"  {col['Field']:<30} {col['Type']:<20} {col['Null']:<5} {col['Key']}")
            
            # Check for any NULL values in critical fields
            cursor.execute("""
                SELECT 
                    SUM(CASE WHEN call_id IS NULL THEN 1 ELSE 0 END) as null_call_id,
                    SUM(CASE WHEN csat IS NULL THEN 1 ELSE 0 END) as null_csat,
                    SUM(CASE WHEN call_verdict IS NULL THEN 1 ELSE 0 END) as null_verdict,
                    SUM(CASE WHEN full_qa_result_json IS NULL THEN 1 ELSE 0 END) as null_qa_json
                FROM qa_results
            """)
            
            null_check = cursor.fetchone()
            logger.info(f"\nNULL value check:")
            logger.info(f"  call_id: {null_check['null_call_id']} NULL values")
            logger.info(f"  csat: {null_check['null_csat']} NULL values")
            logger.info(f"  call_verdict: {null_check['null_verdict']} NULL values")
            logger.info(f"  full_qa_result_json: {null_check['null_qa_json']} NULL values")
            
            cursor.close()
            return True
            
        except MySQLError as e:
            logger.error(f"❌ Error verifying schema: {e}")
            return False
    
    def run_optimization(self):
        """Run the complete schema optimization process"""
        
        logger.info("🚀 Starting MySQL Schema Optimization")
        logger.info("=" * 60)
        logger.info("This will remove unsupported fields that are always NULL")
        logger.info("Fields to be REMOVED:")
        logger.info("  - mongodb_id, pinecone_id")
        logger.info("  - call_date, call_duration, agent_name, customer_name, call_type")
        logger.info("  - overall_score, greeting_score, professionalism_score, etc.")
        logger.info("  - strengths, areas_for_improvement, specific_feedback, compliance_issues")
        logger.info("  - processing_time_seconds, openai_tokens_used, retry_count")
        logger.info("")
        logger.info("Fields to be KEPT:")
        logger.info("  - call_id, folder_id, csat, issue_resolved, call_verdict, call_topic")
        logger.info("  - overall_score_percentage, analyzed_at, analysis_method, openai_model")
        logger.info("  - All JSON fields (full_qa_result_json, assessment_table_json, etc.)")
        logger.info("  - status, error_message")
        logger.info("=" * 60)
        
        try:
            # Step 1: Backup existing data
            backup_data = self.backup_existing_data()
            
            # Step 2: Create optimized table
            self.create_optimized_table()
            
            # Step 3: Migrate data
            self.migrate_data_to_optimized_table(backup_data)
            
            # Step 4: Replace original table
            self.replace_original_table()
            
            # Step 5: Verify optimization
            if self.verify_optimized_schema():
                logger.info("✅ MySQL schema optimization completed successfully!")
                logger.info("\n🎉 Benefits achieved:")
                logger.info("  ✅ Removed 15+ unnecessary NULL fields")
                logger.info("  ✅ Cleaner, more focused table structure")
                logger.info("  ✅ Better performance with fewer columns")
                logger.info("  ✅ Easier maintenance and understanding")
                logger.info("  ✅ All existing data preserved")
                return True
            else:
                logger.error("❌ Schema verification failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Optimization failed: {e}")
            return False
        finally:
            self.db_connections.close_connections()

if __name__ == "__main__":
    print("🔧 MySQL Schema Optimization Tool")
    print("=" * 50)
    print("This will optimize the qa_results table by removing unsupported fields.")
    print("All existing data will be preserved.")
    print("")
    
    confirm = input("Do you want to proceed with schema optimization? (y/N): ").strip().lower()
    if confirm != 'y':
        print("Operation cancelled.")
        sys.exit(0)
    
    optimizer = MySQLSchemaOptimizer()
    success = optimizer.run_optimization()
    
    if success:
        print("\n🎉 Schema optimization completed successfully!")
        print("\nThe qa_results table now contains only supported fields.")
        print("Original table backed up as 'qa_results_backup'.")
    else:
        print("\n❌ Optimization failed. Please check the logs.")
        sys.exit(1)
