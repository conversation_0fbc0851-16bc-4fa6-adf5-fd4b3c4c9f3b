#!/usr/bin/env python3
"""
Fresh End-to-End Test
Clear everything and test 5 records from scratch
"""

import sys
import os
import json
import requests
import time
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def clear_all_data():
    """Clear all existing QA data from MySQL and Pinecone"""
    
    print("🧹 Clearing All Existing Data")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        
        # Clear MySQL
        print("🗑️ Clearing MySQL qa_results table...")
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor()
        cursor.execute('DELETE FROM qa_results')
        mysql_connection.commit()
        cursor.close()
        print("✅ MySQL qa_results table cleared")
        
        # Clear Pinecone autoqa-qa-results index
        print("🗑️ Clearing Pinecone autoqa-qa-results index...")
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        
        # Get all vectors to delete
        query_result = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=10000,
            include_metadata=True
        )
        
        if query_result.matches:
            vector_ids = [match.id for match in query_result.matches]
            print(f"Found {len(vector_ids)} vectors to delete")
            
            # Delete in batches
            batch_size = 100
            for i in range(0, len(vector_ids), batch_size):
                batch = vector_ids[i:i+batch_size]
                qa_results_index.delete(ids=batch)
                print(f"Deleted batch {i//batch_size + 1}/{(len(vector_ids)-1)//batch_size + 1}")
            
            print("✅ Pinecone autoqa-qa-results index cleared")
        else:
            print("✅ Pinecone autoqa-qa-results index was already empty")
        
        db.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ Error clearing data: {e}")
        return False

def run_parallel_processing(limit=5):
    """Run parallel processing on fresh data"""
    
    print(f"\n🚀 Running Parallel Processing for {limit} records")
    print("=" * 60)
    
    try:
        # Import and run parallel processor
        sys.path.append(str(project_root / "scripts"))
        from autoqa_parallel_processor import AutoQAParallelProcessor
        
        processor = AutoQAParallelProcessor(max_workers=2)
        
        print(f"🔄 Starting parallel processing...")
        summary = processor.process_calls_parallel(limit=limit)
        
        if summary['status'] == 'completed':
            print(f"✅ Parallel processing completed!")
            print(f"  Total calls: {summary['total_calls']}")
            print(f"  Successful: {summary['successful']}")
            print(f"  Failed: {summary['failed']}")
            print(f"  Success rate: {summary['success_rate']:.1f}%")
            return summary['successful'] > 0
        else:
            print(f"❌ Parallel processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Error running parallel processing: {e}")
        return False

def check_mysql_data_detailed():
    """Check MySQL data in detail"""
    
    print("\n🔍 Checking MySQL Data (Detailed)")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        
        # Get all records
        cursor.execute('SELECT * FROM qa_results ORDER BY processed_at DESC')
        results = cursor.fetchall()
        
        print(f"Found {len(results)} records in MySQL")
        
        if not results:
            print("❌ No data in MySQL")
            cursor.close()
            db.close_connections()
            return False
        
        # Check each record in detail
        for i, record in enumerate(results, 1):
            print(f"\n📋 Record {i}: {record['call_id']}")
            
            # Check basic fields
            basic_fields = ['csat', 'call_verdict', 'issue_resolved', 'call_topic', 'overall_score_percentage']
            for field in basic_fields:
                value = record[field]
                status = "✅" if value is not None else "❌"
                print(f"  {status} {field}: {value}")
            
            # Check JSON fields
            json_fields = ['full_qa_result_json', 'assessment_table_json', 'agent_feedback_json', 'call_summary_json', 'scores_json']
            for field in json_fields:
                value = record[field]
                if value is not None:
                    try:
                        parsed = json.loads(value)
                        print(f"  ✅ {field}: {len(value)} chars, valid JSON")
                    except json.JSONDecodeError:
                        print(f"  ❌ {field}: {len(value)} chars, INVALID JSON")
                else:
                    print(f"  ❌ {field}: NULL")
            
            # Check other important fields
            other_fields = ['openai_model', 'analysis_method', 'folder_id', 'analyzed_at']
            for field in other_fields:
                value = record[field]
                status = "✅" if value is not None else "❌"
                print(f"  {status} {field}: {value}")
        
        cursor.close()
        db.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ Error checking MySQL data: {e}")
        return False

def check_pinecone_data_detailed():
    """Check Pinecone data in detail"""
    
    print("\n🔍 Checking Pinecone Data (Detailed)")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        
        # Check autoqa-qa-results index
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        stats = qa_results_index.describe_index_stats()
        print(f"autoqa-qa-results vectors: {stats.total_vector_count}")
        
        if stats.total_vector_count == 0:
            print("❌ No data in Pinecone")
            db.close_connections()
            return False
        
        # Get sample data
        query_result = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=10,
            include_metadata=True
        )
        
        for i, match in enumerate(query_result.matches, 1):
            metadata = match.metadata
            call_id = metadata.get('call_id')
            print(f"\n📋 Pinecone Record {i}: {call_id}")
            
            # Check basic fields
            basic_fields = ['csat', 'call_verdict', 'issue_resolved', 'call_topic', 'overall_score_percentage']
            for field in basic_fields:
                value = metadata.get(field)
                status = "✅" if value is not None else "❌"
                print(f"  {status} {field}: {value}")
            
            # Check JSON fields
            json_fields = ['full_qa_result_json', 'assessment_table_json', 'agent_feedback_json']
            for field in json_fields:
                value = metadata.get(field)
                if value is not None:
                    try:
                        parsed = json.loads(value)
                        print(f"  ✅ {field}: {len(value)} chars, valid JSON")
                    except json.JSONDecodeError:
                        print(f"  ❌ {field}: {len(value)} chars, INVALID JSON")
                else:
                    print(f"  ❌ {field}: NULL")
        
        db.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ Error checking Pinecone data: {e}")
        return False

def test_web_console_with_detailed_error():
    """Test web console and capture detailed errors"""
    
    print("\n🧪 Testing Web Console (Detailed Error Capture)")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    try:
        # Test main QA results API
        print("📋 Testing QA results API...")
        response = requests.get(f"{base_url}/api/qa-results", timeout=15)
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ QA results API response received")
                print(f"  Success: {data.get('success')}")
                
                # Check for errors in response
                if 'error' in data:
                    print(f"❌ API returned error: {data['error']}")
                    return False
                
                # Check AutoQA data
                autoqa_qa_results = data.get('autoqa_qa_results', [])
                print(f"  AutoQA QA results count: {len(autoqa_qa_results)}")
                
                if autoqa_qa_results:
                    sample = autoqa_qa_results[0]
                    print(f"  Sample QA result keys: {list(sample.keys())}")
                    
                    # Check for undefined/null values that might cause length errors
                    problematic_fields = []
                    for key, value in sample.items():
                        if value is None:
                            problematic_fields.append(key)
                        elif isinstance(value, str) and value == 'null':
                            problematic_fields.append(key)
                    
                    if problematic_fields:
                        print(f"⚠️ Fields with null values: {problematic_fields}")
                    else:
                        print("✅ No null values found in sample")
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Response content: {response.text[:500]}...")
                return False
        else:
            print(f"❌ QA results API failed: {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Web console request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Fresh End-to-End Test (5 Records)")
    print("=" * 70)
    
    # Step 1: Clear all data
    print("STEP 1: Clear all existing data")
    clear_success = clear_all_data()
    if not clear_success:
        print("❌ Failed to clear data. Exiting.")
        sys.exit(1)
    
    # Step 2: Run parallel processing
    print("\nSTEP 2: Run parallel processing")
    processing_success = run_parallel_processing(limit=5)
    if not processing_success:
        print("❌ Parallel processing failed. Exiting.")
        sys.exit(1)
    
    # Step 3: Check MySQL data
    print("\nSTEP 3: Check MySQL data")
    mysql_success = check_mysql_data_detailed()
    
    # Step 4: Check Pinecone data
    print("\nSTEP 4: Check Pinecone data")
    pinecone_success = check_pinecone_data_detailed()
    
    # Step 5: Test web console
    print("\nSTEP 5: Test web console")
    print("⚠️ Make sure web server is running: python web/qa_results_server.py")
    input("Press Enter when web server is ready...")
    
    web_success = test_web_console_with_detailed_error()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 FRESH END-TO-END TEST SUMMARY:")
    print(f"  Data Clearing: {'✅ PASSED' if clear_success else '❌ FAILED'}")
    print(f"  Parallel Processing: {'✅ PASSED' if processing_success else '❌ FAILED'}")
    print(f"  MySQL Data: {'✅ PASSED' if mysql_success else '❌ FAILED'}")
    print(f"  Pinecone Data: {'✅ PASSED' if pinecone_success else '❌ FAILED'}")
    print(f"  Web Console: {'✅ PASSED' if web_success else '❌ FAILED'}")
    
    if all([clear_success, processing_success, mysql_success, pinecone_success, web_success]):
        print("\n🎉 ALL TESTS PASSED! End-to-end process working correctly!")
    else:
        print("\n❌ SOME TESTS FAILED! Issues identified in the process.")
        sys.exit(1)
