#!/usr/bin/env python3
"""
Check AutoQA Transcripts Index
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def check_autoqa_index():
    """Check the autoqa-transcripts index"""
    
    print("🔍 Checking AutoQA Transcripts Index")
    print("=" * 50)
    
    index_name = os.getenv('PINECONE_INDEX_NAME')
    print(f"Target Index: {index_name}")
    
    try:
        db = DatabaseConnections()
        index = db.get_pinecone_index()
        stats = index.describe_index_stats()
        
        print(f"✅ Index Connection: SUCCESS")
        print(f"Total Vectors: {stats.total_vector_count}")
        print(f"Dimension: {stats.dimension}")
        
        if stats.total_vector_count > 0:
            query_result = index.query(
                vector=[0.0] * 1536,
                top_k=5,
                include_metadata=True
            )
            
            print(f"\nSample Vectors:")
            for i, match in enumerate(query_result.matches, 1):
                metadata = match.metadata
                call_id = metadata.get('call_id', 'Unknown')
                qa_status = metadata.get('qa_status', 'Unknown')
                autoqa_managed = metadata.get('autoqa_managed', False)
                print(f"  {i}. {call_id}: qa_status={qa_status}, autoqa_managed={autoqa_managed}")
        else:
            print(f"\n📝 Index is empty - ready for migration")
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Index Connection: FAILED")
        print(f"Error: {e}")
        
        if "not found" in str(e).lower():
            print(f"\n💡 The 'autoqa-transcripts' index doesn't exist in your Pinecone account.")
            print(f"💡 You need to create it in the Pinecone console with:")
            print(f"   - Name: autoqa-transcripts")
            print(f"   - Dimension: 1536")
            print(f"   - Metric: cosine")

if __name__ == "__main__":
    check_autoqa_index()
