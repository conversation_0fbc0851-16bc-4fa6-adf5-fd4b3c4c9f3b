#!/usr/bin/env python3
"""
Verify QA Analysis Results
Check that QA analysis was stored correctly
"""

import sys
import os
from pathlib import Path

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def verify_qa_results():
    """Verify QA analysis results"""
    
    print("🔍 Verifying QA Analysis Results")
    print("=" * 50)
    
    try:
        db = DatabaseConnections()
        
        # Check qa_analysis collection
        qa_collection = db.get_mongodb_collection('qa_analysis')
        qa_count = qa_collection.count_documents({})
        
        print(f"QA Analysis documents: {qa_count}")
        
        if qa_count > 0:
            print(f"\nQA Analysis Results:")
            for i, doc in enumerate(qa_collection.find().limit(3), 1):
                print(f"\n--- Analysis {i} ---")
                print(f"Call ID: {doc.get('call_id')}")
                print(f"QA Score: {doc.get('call_quality_score')}")
                print(f"Folder ID: {doc.get('folder_id')}")
                print(f"Method: {doc.get('analysis_method')}")
                print(f"Analyzed at: {doc.get('analyzed_at')}")
                
                # Show key analysis fields
                if 'agent_performance' in doc:
                    perf = str(doc['agent_performance'])[:100]
                    print(f"Agent Performance: {perf}...")
        
        # Check Pinecone metadata
        print(f"\n🔍 Pinecone Metadata Updates:")
        index = db.get_pinecone_index()
        
        query_result = index.query(
            vector=[0.0] * 1536,
            top_k=3,
            include_metadata=True
        )
        
        for i, match in enumerate(query_result.matches, 1):
            metadata = match.metadata
            print(f"\nVector {i}:")
            print(f"  Call ID: {metadata.get('call_id')}")
            print(f"  QA Analyzed: {metadata.get('qa_analyzed')}")
            print(f"  QA Score: {metadata.get('qa_score')}")
            print(f"  QA Method: {metadata.get('qa_method')}")
        
        db.close_connections()
        
        if qa_count > 0:
            print(f"\n✅ QA Analysis verification successful!")
            print(f"Found {qa_count} QA analysis results")
        else:
            print(f"\n❌ No QA analysis results found")
        
        return qa_count > 0
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

if __name__ == "__main__":
    verify_qa_results()
