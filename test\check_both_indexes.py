#!/usr/bin/env python3
"""
Check Both AutoQA Pinecone Indexes
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def check_both_indexes():
    """Check both AutoQA Pinecone indexes"""
    
    print("🔍 Checking Both AutoQA Pinecone Indexes")
    print("=" * 60)
    
    transcripts_index = os.getenv('PINECONE_INDEX_NAME', 'autoqa-transcripts')
    qa_results_index = os.getenv('PINECONE_QA_RESULTS_INDEX', 'autoqa-qa-results')
    
    print(f"Transcripts Index: {transcripts_index}")
    print(f"QA Results Index: {qa_results_index}")
    
    try:
        db = DatabaseConnections()
        
        # Check transcripts index
        print(f"\n📋 Checking {transcripts_index}:")
        try:
            transcripts_idx = db.get_pinecone_index(transcripts_index)
            transcripts_stats = transcripts_idx.describe_index_stats()
            
            print(f"  ✅ Connection: SUCCESS")
            print(f"  📊 Total Vectors: {transcripts_stats.total_vector_count}")
            print(f"  📐 Dimension: {transcripts_stats.dimension}")
            
            if transcripts_stats.total_vector_count > 0:
                query_result = transcripts_idx.query(
                    vector=[0.0] * 1536,
                    top_k=3,
                    include_metadata=True
                )
                
                print(f"  📝 Sample Records:")
                for i, match in enumerate(query_result.matches, 1):
                    metadata = match.metadata
                    call_id = metadata.get('call_id', 'Unknown')
                    qa_status = metadata.get('qa_status', 'Unknown')
                    autoqa_managed = metadata.get('autoqa_managed', False)
                    print(f"    {i}. {call_id}: qa_status={qa_status}, autoqa_managed={autoqa_managed}")
            else:
                print(f"  📝 Index is empty")
                
        except Exception as e:
            print(f"  ❌ Connection: FAILED - {e}")
        
        # Check QA results index
        print(f"\n🎯 Checking {qa_results_index}:")
        try:
            qa_results_idx = db.get_pinecone_index(qa_results_index)
            qa_results_stats = qa_results_idx.describe_index_stats()
            
            print(f"  ✅ Connection: SUCCESS")
            print(f"  📊 Total Vectors: {qa_results_stats.total_vector_count}")
            print(f"  📐 Dimension: {qa_results_stats.dimension}")
            
            if qa_results_stats.total_vector_count > 0:
                query_result = qa_results_idx.query(
                    vector=[0.0] * 1536,
                    top_k=3,
                    include_metadata=True
                )
                
                print(f"  📝 Sample QA Results:")
                for i, match in enumerate(query_result.matches, 1):
                    metadata = match.metadata
                    call_id = metadata.get('call_id', 'Unknown')
                    csat = metadata.get('csat', 'Unknown')
                    overall_score = metadata.get('overall_score_percentage', 'Unknown')
                    call_verdict = metadata.get('call_verdict', 'Unknown')
                    print(f"    {i}. {call_id}: CSAT={csat}, Score={overall_score}%, Verdict={call_verdict}")
            else:
                print(f"  📝 Index is empty - no QA results yet")
                
        except Exception as e:
            print(f"  ❌ Connection: FAILED - {e}")
            if "not found" in str(e).lower():
                print(f"  💡 The '{qa_results_index}' index doesn't exist yet.")
                print(f"  💡 Create it in Pinecone console with:")
                print(f"     - Name: {qa_results_index}")
                print(f"     - Dimension: 1536")
                print(f"     - Metric: cosine")
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")

if __name__ == "__main__":
    check_both_indexes()
