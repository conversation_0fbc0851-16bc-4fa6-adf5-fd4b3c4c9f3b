#!/usr/bin/env python3
"""
Check MySQL Null Fields
Identify which fields are null in MySQL and why
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def check_mysql_null_fields():
    """Check which fields are null in MySQL and analyze the data"""
    
    print("🔍 Checking MySQL Null Fields")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        
        # Get all records with all fields
        cursor.execute('SELECT * FROM qa_results')
        results = cursor.fetchall()
        
        if not results:
            print("❌ No data found in MySQL")
            return
        
        print(f"Found {len(results)} records in MySQL")
        print("\n📊 Field Analysis:")
        print("-" * 60)
        
        # Analyze each field for null values
        sample_record = results[0]
        all_fields = list(sample_record.keys())
        
        for field in all_fields:
            null_count = sum(1 for record in results if record[field] is None)
            non_null_count = len(results) - null_count
            
            status = "❌ ALL NULL" if null_count == len(results) else "⚠️ SOME NULL" if null_count > 0 else "✅ NO NULL"
            
            print(f"{status} {field}: {non_null_count}/{len(results)} have values")
            
            # Show sample values for non-null fields
            if non_null_count > 0:
                sample_values = [record[field] for record in results if record[field] is not None][:3]
                if field.endswith('_json') and sample_values:
                    print(f"    Sample: [JSON data - {len(str(sample_values[0]))} chars]")
                else:
                    print(f"    Sample: {sample_values}")
        
        print("\n📋 Detailed Record Analysis:")
        print("-" * 60)
        
        for i, record in enumerate(results, 1):
            print(f"\nRecord {i}: {record['call_id']}")
            print(f"  CSAT: {record['csat']}")
            print(f"  Call Verdict: {record['call_verdict']}")
            print(f"  Issue Resolved: {record['issue_resolved']}")
            print(f"  Call Topic: {record['call_topic']}")
            print(f"  Overall Score: {record['overall_score_percentage']}%")
            print(f"  Folder ID: {record['folder_id']}")
            print(f"  Analysis Method: {record['analysis_method']}")
            print(f"  Analyzed At: {record['analyzed_at']}")
            print(f"  OpenAI Model: {record['openai_model']}")
            print(f"  Status: {record['status']}")
            
            # Check JSON fields
            json_fields = ['full_qa_result_json', 'assessment_table_json', 'agent_feedback_json', 'call_summary_json', 'scores_json']
            for json_field in json_fields:
                if record[json_field]:
                    print(f"  {json_field}: {len(record[json_field])} chars")
                else:
                    print(f"  {json_field}: NULL")
            
            # Check other important fields that might be null
            important_fields = ['call_date', 'call_duration', 'agent_name', 'customer_name', 'call_type']
            null_important = [field for field in important_fields if record[field] is None]
            if null_important:
                print(f"  NULL important fields: {null_important}")
        
        cursor.close()
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Error checking MySQL fields: {e}")

def compare_pinecone_vs_mysql():
    """Compare Pinecone data with MySQL to see what's missing"""
    
    print("\n🔍 Comparing Pinecone vs MySQL Data")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        
        # Get Pinecone data
        qa_results_index = db.get_pinecone_index('autoqa-qa-results')
        query_result = qa_results_index.query(
            vector=[0.0] * 1536,
            top_k=5,
            include_metadata=True
        )
        
        print("📊 Pinecone Data Sample:")
        for i, match in enumerate(query_result.matches, 1):
            metadata = match.metadata
            call_id = metadata.get('call_id')
            print(f"\nPinecone Record {i}: {call_id}")
            print(f"  CSAT: {metadata.get('csat')}")
            print(f"  Call Verdict: {metadata.get('call_verdict')}")
            print(f"  Issue Resolved: {metadata.get('issue_resolved')}")
            print(f"  Call Topic: {metadata.get('call_topic')}")
            print(f"  Overall Score: {metadata.get('overall_score_percentage')}%")
            print(f"  Folder ID: {metadata.get('folder_id')}")
            print(f"  Analysis Method: {metadata.get('analysis_method')}")
            print(f"  Analyzed At: {metadata.get('analyzed_at')}")
            
            # Check for JSON fields
            if metadata.get('full_qa_result_json'):
                print(f"  Full QA JSON: {len(metadata['full_qa_result_json'])} chars")
            else:
                print(f"  Full QA JSON: Missing")
            
            if metadata.get('assessment_table_json'):
                print(f"  Assessment Table JSON: {len(metadata['assessment_table_json'])} chars")
            else:
                print(f"  Assessment Table JSON: Missing")
        
        # Get MySQL data for comparison
        mysql_connection = db.connect_mysql()
        cursor = mysql_connection.cursor(dictionary=True)
        cursor.execute('SELECT call_id, csat, call_verdict, issue_resolved, call_topic, overall_score_percentage, folder_id, analysis_method, analyzed_at FROM qa_results')
        mysql_results = cursor.fetchall()
        cursor.close()
        
        print("\n📊 MySQL Data Sample:")
        for i, record in enumerate(mysql_results, 1):
            print(f"\nMySQL Record {i}: {record['call_id']}")
            print(f"  CSAT: {record['csat']}")
            print(f"  Call Verdict: {record['call_verdict']}")
            print(f"  Issue Resolved: {record['issue_resolved']}")
            print(f"  Call Topic: {record['call_topic']}")
            print(f"  Overall Score: {record['overall_score_percentage']}%")
            print(f"  Folder ID: {record['folder_id']}")
            print(f"  Analysis Method: {record['analysis_method']}")
            print(f"  Analyzed At: {record['analyzed_at']}")
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Error comparing data: {e}")

if __name__ == "__main__":
    print("🚀 MySQL Field Analysis")
    print("=" * 70)
    
    check_mysql_null_fields()
    compare_pinecone_vs_mysql()
    
    print("\n💡 RECOMMENDATIONS:")
    print("1. Check migration script field mapping")
    print("2. Verify Pinecone metadata has all required fields")
    print("3. Update web console to handle null values gracefully")
    print("4. Re-run migration with corrected field mapping")
