#!/usr/bin/env python3
"""
Check Pinecone Configuration and Index Status
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def check_pinecone_config():
    """Check Pinecone configuration and current status"""
    
    print("🔍 Checking Pinecone Configuration")
    print("=" * 50)
    
    # Check environment variables
    pinecone_api_key = os.getenv('PINECONE_API_KEY', 'Not set')
    pinecone_index = os.getenv('PINECONE_INDEX_NAME', 'sourav-chat1 (default)')
    
    print(f"PINECONE_API_KEY: {pinecone_api_key[:20]}..." if len(pinecone_api_key) > 20 else f"PINECONE_API_KEY: {pinecone_api_key}")
    print(f"PINECONE_INDEX_NAME: {pinecone_index}")
    
    try:
        db = DatabaseConnections()
        
        # Test Pinecone connection
        index = db.get_pinecone_index()
        stats = index.describe_index_stats()
        
        print(f"\n✅ Pinecone Connection: SUCCESS")
        print(f"Total Vectors: {stats.total_vector_count}")
        print(f"Dimension: {stats.dimension}")
        
        # Check for AutoQA managed vectors
        query_result = index.query(
            vector=[0.0] * 1536,
            top_k=10,
            include_metadata=True,
            filter={'autoqa_managed': True}
        )
        
        print(f"\nAutoQA Managed Vectors: {len(query_result.matches)}")
        for i, match in enumerate(query_result.matches, 1):
            metadata = match.metadata
            call_id = metadata.get('call_id', 'Unknown')
            qa_analyzed = metadata.get('qa_analyzed', False)
            qa_score = metadata.get('qa_score', 'N/A')
            print(f"  {i}. {call_id}: QA Analyzed={qa_analyzed}, Score={qa_score}")
        
        # Check all vectors (not just AutoQA managed)
        all_query = index.query(
            vector=[0.0] * 1536,
            top_k=10,
            include_metadata=True
        )
        
        print(f"\nAll Vectors: {len(all_query.matches)}")
        for i, match in enumerate(all_query.matches, 1):
            metadata = match.metadata
            call_id = metadata.get('call_id', 'Unknown')
            autoqa_managed = metadata.get('autoqa_managed', False)
            print(f"  {i}. {call_id}: AutoQA Managed={autoqa_managed}")
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Pinecone Connection: FAILED")
        print(f"Error: {e}")

if __name__ == "__main__":
    check_pinecone_config()
