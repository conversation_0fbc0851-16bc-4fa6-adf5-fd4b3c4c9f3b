#!/usr/bin/env python3
"""
Debug QA Analysis Structure
Check what's actually being stored in MongoDB
"""

import sys
import os
from pathlib import Path
import json

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

def debug_qa_analysis():
    """Debug QA analysis structure"""
    
    print("🔍 Debugging QA Analysis Structure")
    print("=" * 60)
    
    try:
        db = DatabaseConnections()
        qa_collection = db.get_mongodb_collection('qa_analysis')
        
        qa_count = qa_collection.count_documents({})
        print(f"QA Analysis documents: {qa_count}")
        
        if qa_count > 0:
            # Get the latest document
            qa_doc = qa_collection.find_one(sort=[('analyzed_at', -1)])
            
            print(f"\n📋 Latest QA Document Structure:")
            print(f"Call ID: {qa_doc.get('call_id')}")
            print(f"Analysis Method: {qa_doc.get('analysis_method')}")
            print(f"Analyzed At: {qa_doc.get('analyzed_at')}")
            
            print(f"\n🔑 Top-level Keys:")
            for key in sorted(qa_doc.keys()):
                if key == '_id':
                    continue
                value = qa_doc[key]
                if isinstance(value, dict):
                    print(f"  {key}: dict with {len(value)} keys")
                elif isinstance(value, list):
                    print(f"  {key}: list with {len(value)} items")
                elif isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: string ({len(value)} chars)")
                else:
                    print(f"  {key}: {type(value).__name__} = {value}")
            
            # Check for comprehensive QA fields
            comprehensive_fields = ['CSAT', 'Call Verdict', 'Issue Resolved', 'Call Topic', 'Assessment Table', 'Scores']
            
            print(f"\n📊 Comprehensive QA Fields Check:")
            for field in comprehensive_fields:
                if field in qa_doc:
                    value = qa_doc[field]
                    if isinstance(value, dict):
                        print(f"  ✅ {field}: dict with keys {list(value.keys())}")
                    elif isinstance(value, list):
                        print(f"  ✅ {field}: list with {len(value)} items")
                    else:
                        print(f"  ✅ {field}: {value}")
                else:
                    print(f"  ❌ {field}: Missing")
            
            # Check Scores structure specifically
            if 'Scores' in qa_doc:
                scores = qa_doc['Scores']
                print(f"\n🎯 Scores Structure Detail:")
                print(f"  Scores type: {type(scores)}")
                if isinstance(scores, dict):
                    for key, value in scores.items():
                        print(f"    {key}: {value}")
                        if key == 'Overall Score' and isinstance(value, dict):
                            for sub_key, sub_value in value.items():
                                print(f"      {sub_key}: {sub_value}")
            
            # Check if there's raw response
            if 'raw_response' in qa_doc:
                raw_response = qa_doc['raw_response']
                print(f"\n📝 Raw Response Preview:")
                print(f"  Length: {len(raw_response)} characters")
                print(f"  First 200 chars: {raw_response[:200]}...")
                
                # Try to parse as JSON
                try:
                    parsed = json.loads(raw_response)
                    print(f"  ✅ Raw response is valid JSON")
                    print(f"  JSON keys: {list(parsed.keys()) if isinstance(parsed, dict) else 'Not a dict'}")
                except json.JSONDecodeError as e:
                    print(f"  ❌ Raw response is not valid JSON: {e}")
            
            # Check overall_score_numeric
            if 'overall_score_numeric' in qa_doc:
                print(f"\n🔢 Numeric Score: {qa_doc['overall_score_numeric']}")
            else:
                print(f"\n❌ overall_score_numeric: Not found")
        
        db.close_connections()
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_qa_analysis()
