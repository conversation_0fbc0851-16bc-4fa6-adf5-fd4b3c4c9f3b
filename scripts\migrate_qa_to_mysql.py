#!/usr/bin/env python3
"""
Migrate Existing QA Results to MySQL
Migrate QA analysis data from Pinecone autoqa-qa-results index to MySQL database
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from tqdm import tqdm

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QAToMySQLMigrator:
    """Migrate QA results from Pinecone to MySQL"""
    
    def __init__(self):
        self.db_connections = DatabaseConnections()
    
    def migrate_qa_results_to_mysql(self, limit: int = None, dry_run: bool = False):
        """Migrate QA results from autoqa-qa-results Pinecone index to MySQL"""
        
        logger.info("🔄 Migrating QA results from Pinecone to MySQL...")
        logger.info("=" * 60)
        
        try:
            # Get QA results index
            qa_results_index = self.db_connections.get_pinecone_index('autoqa-qa-results')
            
            # Query all QA result vectors
            query_result = qa_results_index.query(
                vector=[0.0] * 1536,
                top_k=10000,  # Get all records
                include_metadata=True
            )
            
            total_vectors = len(query_result.matches)
            if limit:
                total_vectors = min(total_vectors, limit)
            
            logger.info(f"Found {total_vectors} QA result vectors to migrate")
            
            migrated_count = 0
            skipped_count = 0
            error_count = 0
            
            # Connect to MySQL
            if not dry_run:
                mysql_connection = self.db_connections.connect_mysql()
                cursor = mysql_connection.cursor()
            
            with tqdm(total=total_vectors, desc="Migrating QA results") as pbar:
                for i, match in enumerate(query_result.matches[:total_vectors]):
                    try:
                        vector_id = match.id
                        metadata = match.metadata
                        call_id = metadata.get('call_id')
                        
                        if not call_id:
                            logger.warning(f"No call_id in metadata for vector {vector_id}")
                            error_count += 1
                            pbar.update(1)
                            continue
                        
                        # Check if already exists in MySQL
                        if not dry_run:
                            check_query = "SELECT id FROM qa_results WHERE call_id = %s"
                            cursor.execute(check_query, (call_id,))
                            existing = cursor.fetchone()
                            
                            if existing:
                                logger.info(f"⏭️ Skipping {call_id} - already exists in MySQL")
                                skipped_count += 1
                                pbar.update(1)
                                continue
                        
                        # Prepare MySQL data from Pinecone metadata
                        mysql_data = self.prepare_mysql_data_from_metadata(metadata)
                        
                        if not mysql_data:
                            logger.warning(f"Could not prepare MySQL data for {call_id}")
                            error_count += 1
                            pbar.update(1)
                            continue
                        
                        if not dry_run:
                            # Insert into MySQL with ON DUPLICATE KEY UPDATE
                            insert_query = """INSERT INTO qa_results (call_id, folder_id, csat, issue_resolved, call_verdict, call_topic, overall_score_percentage, analyzed_at, analysis_method, full_qa_result_json, assessment_table_json, call_summary_json, agent_feedback_json, scores_json, openai_model, status) VALUES (%(call_id)s, %(folder_id)s, %(csat)s, %(issue_resolved)s, %(call_verdict)s, %(call_topic)s, %(overall_score_percentage)s, %(analyzed_at)s, %(analysis_method)s, %(full_qa_result_json)s, %(assessment_table_json)s, %(call_summary_json)s, %(agent_feedback_json)s, %(scores_json)s, %(openai_model)s, 'completed') ON DUPLICATE KEY UPDATE call_summary_json = VALUES(call_summary_json), scores_json = VALUES(scores_json), openai_model = VALUES(openai_model), processed_at = CURRENT_TIMESTAMP"""
                            
                            cursor.execute(insert_query, mysql_data)
                            mysql_connection.commit()
                        
                        migrated_count += 1
                        logger.info(f"✅ Migrated QA data for {call_id}")
                        
                    except Exception as e:
                        logger.error(f"❌ Error migrating QA data for vector {vector_id}: {e}")
                        error_count += 1
                    
                    pbar.update(1)
            
            if not dry_run:
                cursor.close()
            
            # Summary
            logger.info("=" * 60)
            logger.info("📊 QA to MySQL Migration Summary:")
            logger.info(f"  Total vectors processed: {total_vectors}")
            logger.info(f"  Migrated: {migrated_count}")
            logger.info(f"  Skipped (already exists): {skipped_count}")
            logger.info(f"  Errors: {error_count}")
            
            if dry_run:
                logger.info("🧪 DRY RUN - No data was written to MySQL")
            else:
                logger.info("✅ QA to MySQL migration completed!")
            
            return migrated_count > 0
            
        except Exception as e:
            logger.error(f"❌ Failed to migrate QA results to MySQL: {e}")
            return False
    
    def prepare_mysql_data_from_metadata(self, metadata: Dict) -> Optional[Dict]:
        """Prepare MySQL data from Pinecone metadata with enhanced field extraction"""
        try:
            call_id = metadata.get('call_id')
            if not call_id:
                return None

            # Basic fields from metadata
            mysql_data = {
                'call_id': call_id,
                'folder_id': metadata.get('folder_id'),
                'csat': metadata.get('csat', 'Unknown'),
                'issue_resolved': metadata.get('issue_resolved', 'Unknown'),
                'call_verdict': metadata.get('call_verdict', 'Unknown'),
                'call_topic': metadata.get('call_topic', 'Unknown'),
                'overall_score_percentage': metadata.get('overall_score_percentage', 0),
                'analyzed_at': metadata.get('analyzed_at'),
                'analysis_method': metadata.get('analysis_method'),
                'full_qa_result_json': metadata.get('full_qa_result_json'),
                'assessment_table_json': metadata.get('assessment_table_json'),
                'agent_feedback_json': metadata.get('agent_feedback_json'),

                # Extract call metadata if available
                'call_date': metadata.get('original_call_datetime'),
                'call_duration': metadata.get('original_duration'),

                # Set OpenAI model
                'openai_model': 'gpt-4o-mini'
            }

            # Extract additional fields from full_qa_result_json if available
            if metadata.get('full_qa_result_json'):
                try:
                    qa_result = json.loads(metadata['full_qa_result_json'])

                    # Extract Call Summary JSON
                    if 'Call Summary' in qa_result:
                        mysql_data['call_summary_json'] = json.dumps(qa_result['Call Summary'], ensure_ascii=False)

                    # Extract Scores JSON
                    if 'Scores' in qa_result:
                        mysql_data['scores_json'] = json.dumps(qa_result['Scores'], ensure_ascii=False)

                        # Extract individual scores from Scores section
                        scores = qa_result['Scores']
                        if isinstance(scores, dict) and 'Category Scores' in scores:
                            category_scores = scores['Category Scores']
                            for category, score_data in category_scores.items():
                                if isinstance(score_data, dict) and 'Score' in score_data:
                                    score_value = score_data['Score']
                                    # Map to MySQL fields based on category name
                                    category_lower = category.lower()
                                    if 'greeting' in category_lower:
                                        mysql_data['greeting_score'] = score_value
                                    elif 'professionalism' in category_lower:
                                        mysql_data['professionalism_score'] = score_value
                                    elif 'problem' in category_lower or 'resolution' in category_lower:
                                        mysql_data['problem_resolution_score'] = score_value
                                    elif 'closing' in category_lower:
                                        mysql_data['closing_score'] = score_value

                        # Extract overall score details
                        if 'Overall Score' in scores:
                            overall_score = scores['Overall Score']
                            if isinstance(overall_score, dict):
                                mysql_data['overall_score'] = overall_score.get('Overall Score', 0)

                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse full_qa_result_json for {call_id}: {e}")

            return mysql_data

        except Exception as e:
            logger.error(f"Error preparing MySQL data: {e}")
            return None
    
    def verify_mysql_data(self, limit: int = 10):
        """Verify that data was migrated correctly to MySQL"""
        
        logger.info("🔍 Verifying MySQL QA data...")
        logger.info("=" * 60)
        
        try:
            mysql_connection = self.db_connections.connect_mysql()
            cursor = mysql_connection.cursor(dictionary=True)
            
            # Get sample records
            query = """
            SELECT call_id, csat, call_verdict, issue_resolved, call_topic, 
                   overall_score_percentage, analyzed_at, analysis_method,
                   CHAR_LENGTH(full_qa_result_json) as json_length,
                   CHAR_LENGTH(assessment_table_json) as assessment_length,
                   CHAR_LENGTH(agent_feedback_json) as feedback_length
            FROM qa_results 
            ORDER BY analyzed_at DESC 
            LIMIT %s
            """
            
            cursor.execute(query, (limit,))
            results = cursor.fetchall()
            
            logger.info(f"📊 Found {len(results)} QA records in MySQL:")
            logger.info("-" * 60)
            
            for row in results:
                logger.info(f"Call ID: {row['call_id']}")
                logger.info(f"  CSAT: {row['csat']}")
                logger.info(f"  Verdict: {row['call_verdict']}")
                logger.info(f"  Issue Resolved: {row['issue_resolved']}")
                logger.info(f"  Topic: {row['call_topic']}")
                logger.info(f"  Score: {row['overall_score_percentage']}%")
                logger.info(f"  Method: {row['analysis_method']}")
                logger.info(f"  JSON Length: {row['json_length']} chars")
                logger.info(f"  Assessment Length: {row['assessment_length']} chars")
                logger.info(f"  Feedback Length: {row['feedback_length']} chars")
                logger.info("-" * 40)
            
            # Get summary statistics
            stats_query = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN full_qa_result_json IS NOT NULL THEN 1 END) as with_full_json,
                COUNT(CASE WHEN assessment_table_json IS NOT NULL THEN 1 END) as with_assessment,
                COUNT(CASE WHEN agent_feedback_json IS NOT NULL THEN 1 END) as with_feedback,
                AVG(overall_score_percentage) as avg_score
            FROM qa_results
            """
            
            cursor.execute(stats_query)
            stats = cursor.fetchone()
            
            logger.info("📈 MySQL QA Data Statistics:")
            logger.info(f"  Total Records: {stats['total_records']}")
            logger.info(f"  With Full JSON: {stats['with_full_json']}")
            logger.info(f"  With Assessment: {stats['with_assessment']}")
            logger.info(f"  With Feedback: {stats['with_feedback']}")
            logger.info(f"  Average Score: {stats['avg_score']:.2f}%")
            
            cursor.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Error verifying MySQL data: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrate QA Results to MySQL")
    parser.add_argument('--limit', type=int, help='Limit number of records to migrate')
    parser.add_argument('--dry-run', action='store_true', help='Run without making changes')
    parser.add_argument('--verify', action='store_true', help='Verify existing MySQL data')
    
    args = parser.parse_args()
    
    try:
        migrator = QAToMySQLMigrator()
        
        if args.verify:
            print(f"\n🔍 Verifying MySQL QA Data:")
            migrator.verify_mysql_data()
            return
        
        print(f"\n🔄 QA to MySQL Migration Configuration:")
        print(f"  Limit: {args.limit or 'No limit'}")
        print(f"  Dry Run: {'Yes' if args.dry_run else 'No'}")
        
        if not args.dry_run:
            confirm = input(f"\nProceed with QA migration to MySQL? (y/N): ").strip().lower()
            if confirm != 'y':
                print("Migration cancelled.")
                return
        
        success = migrator.migrate_qa_results_to_mysql(
            limit=args.limit,
            dry_run=args.dry_run
        )
        
        if success:
            print(f"\n🎉 QA to MySQL migration completed successfully!")
            if not args.dry_run:
                print(f"\n🔍 Verifying migrated data...")
                migrator.verify_mysql_data()
        else:
            print(f"\n❌ QA to MySQL migration completed with errors.")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
