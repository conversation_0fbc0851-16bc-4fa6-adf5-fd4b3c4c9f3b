#!/usr/bin/env python3
"""
Enhanced MongoDB to Pinecone Migration
Combines data from multiple collections for maximum metadata coverage
"""

import sys
import os
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from tqdm import tqdm

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections
import openai
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedMongoToPineconeMigrator:
    """Enhanced migrator that combines multiple collections"""
    
    def __init__(self):
        self.db_connections = DatabaseConnections()
        self.openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Test OpenAI connection
        try:
            test_response = self.openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input="test"
            )
            logger.info("✅ OpenAI client initialized successfully")
        except Exception as e:
            logger.error(f"❌ OpenAI initialization failed: {e}")
            raise
    
    def get_combined_document_data(self, call_id: str) -> Dict[str, Any]:
        """Get combined data from multiple collections for a call_id"""
        
        combined_data = {
            'call_id': call_id,
            'sources': []
        }
        
        # Collection mapping: collection_name -> priority (higher = more important)
        collections_to_check = {
            'call_smart_speech_transcribe': 10,  # Primary transcript source
            'call_smart_speech_details': 9,     # Primary metadata source
            'call_smart_speech_details_2': 8,   # Secondary metadata
            'call_smart_speech_details_3': 7,   # Tertiary metadata
        }
        
        database = self.db_connections.get_mongodb_database()
        
        for collection_name, priority in collections_to_check.items():
            try:
                collection = database[collection_name]
                doc = collection.find_one({'call_id': call_id})
                
                if doc:
                    combined_data['sources'].append({
                        'collection': collection_name,
                        'priority': priority,
                        'data': doc
                    })
                    logger.debug(f"Found data in {collection_name} for call_id: {call_id}")
                
            except Exception as e:
                logger.warning(f"Error checking {collection_name}: {e}")
                continue
        
        # Sort sources by priority (highest first)
        combined_data['sources'].sort(key=lambda x: x['priority'], reverse=True)
        
        return combined_data
    
    def merge_document_data(self, combined_data: Dict[str, Any]) -> Dict[str, Any]:
        """Merge data from multiple sources with priority-based field selection"""
        
        if not combined_data['sources']:
            return None
        
        merged_doc = {
            'call_id': combined_data['call_id'],
            '_combined_sources': [s['collection'] for s in combined_data['sources']]
        }
        
        # Field priority mapping - which collection to prefer for each field
        field_priorities = {
            # Transcript fields - prefer call_smart_speech_transcribe
            'tran_text': ['call_smart_speech_transcribe'],
            'agent_diarize_transcript': ['call_smart_speech_transcribe'],
            'client_diarize_transcript': ['call_smart_speech_transcribe'],
            'speaker_diarize_transcript': ['call_smart_speech_transcribe'],
            'diarize_transcript': ['call_smart_speech_transcribe'],
            'transcribe_date': ['call_smart_speech_transcribe'],
            'json_data': ['call_smart_speech_transcribe'],
            
            # Metadata fields - prefer call_smart_speech_details
            'folder_id': ['call_smart_speech_details', 'call_smart_speech_details_2', 'call_smart_speech_details_3'],
            'datetime': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'call_datetime': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'duration': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'meta_data': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'agent_emotion': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'client_emotion': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'overall_emotion': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'agent_gender': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'client_gender': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'call_quality': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'csat': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'call_topic': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'call_verdict': ['call_smart_speech_details', 'call_smart_speech_details_2'],
            'sentiment_shift': ['call_smart_speech_details', 'call_smart_speech_details_2'],
        }
        
        # Merge fields based on priority
        for source_info in combined_data['sources']:
            source_collection = source_info['collection']
            source_data = source_info['data']
            
            for field, value in source_data.items():
                if field == '_id':
                    # Store original IDs with collection prefix
                    merged_doc[f'{source_collection}_id'] = str(value)
                    continue
                
                # Skip if field already exists and current source is not preferred
                if field in merged_doc:
                    preferred_collections = field_priorities.get(field, [])
                    if preferred_collections:
                        # Check if current source is preferred over existing
                        current_source_priority = float('inf')
                        for i, preferred in enumerate(preferred_collections):
                            if source_collection == preferred:
                                current_source_priority = i
                                break
                        
                        # Skip if current source has lower priority
                        existing_source = merged_doc.get(f'_{field}_source')
                        if existing_source:
                            existing_priority = float('inf')
                            for i, preferred in enumerate(preferred_collections):
                                if existing_source == preferred:
                                    existing_priority = i
                                    break
                            
                            if current_source_priority >= existing_priority:
                                continue
                
                # Add the field
                merged_doc[field] = value
                merged_doc[f'_{field}_source'] = source_collection
        
        return merged_doc
    
    def extract_text_content(self, document: Dict) -> str:
        """Extract comprehensive text content from merged document"""
        
        text_parts = []
        
        # Main transcript
        if 'tran_text' in document and document['tran_text']:
            text_parts.append(f"COMPLETE TRANSCRIPT:\n{document['tran_text']}")
        
        # Diarized transcripts
        diarize_fields = [
            ('agent_diarize_transcript', 'AGENT SIDE'),
            ('client_diarize_transcript', 'CLIENT SIDE'),
            ('speaker_diarize_transcript', 'SPEAKER ANALYSIS')
        ]
        
        for field, label in diarize_fields:
            if field in document and document[field]:
                if isinstance(document[field], dict) and 'text' in document[field]:
                    text_parts.append(f"{label}:\n{document[field]['text']}")
                elif isinstance(document[field], str):
                    text_parts.append(f"{label}:\n{document[field]}")
        
        # Diarize transcript (conversation flow)
        if 'diarize_transcript' in document and document['diarize_transcript']:
            if isinstance(document['diarize_transcript'], list):
                diarize_text = []
                for segment in document['diarize_transcript']:
                    if isinstance(segment, dict):
                        speaker = segment.get('speaker', 'Unknown')
                        text = segment.get('text', '')
                        diarize_text.append(f"{speaker}: {text}")
                    elif isinstance(segment, str):
                        diarize_text.append(segment)
                
                if diarize_text:
                    text_parts.append(f"CONVERSATION FLOW:\n" + "\n".join(diarize_text))
        
        return '\n\n'.join(text_parts)
    
    def prepare_enhanced_metadata(self, document: Dict) -> Dict:
        """Prepare enhanced metadata from merged document"""

        # Get the primary mongodb_id (prefer transcript collection)
        mongodb_id = (document.get('call_smart_speech_transcribe_id') or
                     document.get('call_smart_speech_details_id') or
                     document.get('call_smart_speech_details_2_id') or
                     str(document.get('_id', 'unknown')))

        metadata = {
            'mongodb_id': mongodb_id,
            'source': 'enhanced_mongodb_migration',
            'migrated_at': datetime.now().isoformat(),
            'autoqa_managed': True,
            'combined_sources': ','.join(document.get('_combined_sources', []))
        }
        
        # Core fields
        core_fields = [
            'call_id', 'folder_id', 'transcribe_date', 'datetime', 'call_datetime',
            'duration', 'agent_emotion', 'client_emotion', 'overall_emotion',
            'agent_gender', 'client_gender', 'call_quality', 'csat',
            'call_topic', 'call_verdict', 'sentiment_shift'
        ]
        
        for field in core_fields:
            if field in document and document[field] is not None:
                value = str(document[field])
                if len(value) > 500:  # Pinecone metadata limit
                    value = value[:500]
                metadata[field] = value
        
        # ✅ CRITICAL FIX: Store complete transcript data in metadata
        if 'tran_text' in document and document['tran_text']:
            # Store full transcript text (truncate if too long for Pinecone)
            tran_text = str(document['tran_text'])
            metadata['tran_text'] = tran_text[:30000] if len(tran_text) > 30000 else tran_text
            metadata['transcript_length'] = len(tran_text)

        # ✅ CRITICAL FIX: Store diarized transcript data
        diarize_fields = ['agent_diarize_transcript', 'client_diarize_transcript', 'speaker_diarize_transcript', 'diarize_transcript']
        for field in diarize_fields:
            if field in document and document[field]:
                if isinstance(document[field], dict) and 'text' in document[field]:
                    # Extract text from dict structure
                    diarize_text = str(document[field]['text'])
                    metadata[field] = diarize_text[:10000] if len(diarize_text) > 10000 else diarize_text
                elif isinstance(document[field], str):
                    # Direct string value
                    diarize_text = str(document[field])
                    metadata[field] = diarize_text[:10000] if len(diarize_text) > 10000 else diarize_text
                elif isinstance(document[field], list):
                    # List of segments - join them
                    diarize_text = ' '.join([str(item) for item in document[field] if item])
                    metadata[field] = diarize_text[:10000] if len(diarize_text) > 10000 else diarize_text

        if 'diarize_transcript' in document and isinstance(document['diarize_transcript'], list):
            metadata['conversation_segments'] = len(document['diarize_transcript'])

        # Add diarize type indicators
        diarize_types = ['agent', 'client', 'speaker']
        for dtype in diarize_types:
            field = f'{dtype}_diarize_transcript'
            if field in document:
                metadata[f'{dtype}_type'] = dtype
        
        # Parse JSON data for additional metadata
        if 'json_data' in document and document['json_data']:
            try:
                json_data = json.loads(document['json_data'])
                
                # Extract key JSON fields
                json_fields = ['emotion', 'asr', 'donedate', 'nchannels', 'started']
                for field in json_fields:
                    if field in json_data:
                        metadata[f'json_{field}'] = str(json_data[field])[:100]
                        
            except json.JSONDecodeError:
                pass
        
        return metadata
    
    def migrate_enhanced_documents(self, limit: int = None, dry_run: bool = False):
        """Migrate documents with enhanced data combination"""
        
        logger.info("🚀 Starting enhanced MongoDB to Pinecone migration...")
        logger.info("=" * 60)
        
        # Get primary collection (transcript source)
        primary_collection = self.db_connections.get_mongodb_collection('call_smart_speech_transcribe')
        
        # Count documents
        total_docs = primary_collection.count_documents({})
        if limit:
            total_docs = min(total_docs, limit)
        
        logger.info(f"Total documents to process: {total_docs}")
        
        # Get Pinecone index
        index = self.db_connections.get_pinecone_index()
        
        # Process documents
        processed = 0
        successful = 0
        failed = 0
        
        query = {}
        cursor = primary_collection.find(query)
        if limit:
            cursor = cursor.limit(limit)
        
        with tqdm(total=total_docs, desc="Migrating documents") as pbar:
            for doc in cursor:
                try:
                    call_id = doc.get('call_id')
                    if not call_id:
                        logger.warning(f"Document missing call_id: {doc.get('_id')}")
                        failed += 1
                        continue
                    
                    # Get combined data from multiple collections
                    combined_data = self.get_combined_document_data(call_id)
                    
                    if not combined_data['sources']:
                        logger.warning(f"No data found for call_id: {call_id}")
                        failed += 1
                        continue
                    
                    # Merge data from multiple sources
                    merged_doc = self.merge_document_data(combined_data)
                    
                    if not merged_doc:
                        logger.warning(f"Failed to merge data for call_id: {call_id}")
                        failed += 1
                        continue
                    
                    # Extract text content
                    text_content = self.extract_text_content(merged_doc)
                    
                    if not text_content.strip():
                        logger.warning(f"No text content for call_id: {call_id}")
                        failed += 1
                        continue
                    
                    # Generate embedding
                    response = self.openai_client.embeddings.create(
                        model="text-embedding-ada-002",
                        input=text_content
                    )
                    embedding = response.data[0].embedding
                    
                    # Prepare metadata
                    metadata = self.prepare_enhanced_metadata(merged_doc)
                    
                    if not dry_run:
                        # Create vector ID using the mongodb_id from metadata
                        vector_id = f"autoqa-enhanced-{metadata['mongodb_id']}"

                        # Upsert to Pinecone
                        index.upsert(vectors=[{
                            'id': vector_id,
                            'values': embedding,
                            'metadata': metadata
                        }])
                    
                    successful += 1
                    processed += 1
                    
                    if processed % 10 == 0:
                        logger.info(f"Processed {processed} documents...")
                    
                except Exception as e:
                    logger.error(f"Error processing call_id {call_id}: {e}")
                    failed += 1
                
                pbar.update(1)
        
        # Summary
        logger.info("=" * 60)
        logger.info("📊 Enhanced Migration Summary:")
        logger.info(f"  Total documents: {total_docs}")
        logger.info(f"  Processed: {processed}")
        logger.info(f"  Successful: {successful}")
        logger.info(f"  Failed: {failed}")
        
        if dry_run:
            logger.info("🧪 DRY RUN - No data was written to Pinecone")
        else:
            logger.info("✅ Enhanced migration completed successfully!")
        
        return successful == processed

def main():
    parser = argparse.ArgumentParser(description="Enhanced MongoDB to Pinecone Migration")
    parser.add_argument('--limit', type=int, help='Limit number of documents to process')
    parser.add_argument('--dry-run', action='store_true', help='Run without writing to Pinecone')
    
    args = parser.parse_args()
    
    try:
        migrator = EnhancedMongoToPineconeMigrator()
        
        print(f"\n🚀 Enhanced Migration Configuration:")
        print(f"  Limit: {args.limit or 'No limit'}")
        print(f"  Dry Run: {'Yes' if args.dry_run else 'No'}")
        print(f"  Collections: call_smart_speech_transcribe + call_smart_speech_details + more")
        
        if not args.dry_run:
            confirm = input(f"\nProceed with enhanced migration? (y/N): ").strip().lower()
            if confirm != 'y':
                print("Migration cancelled.")
                return
        
        success = migrator.migrate_enhanced_documents(
            limit=args.limit,
            dry_run=args.dry_run
        )
        
        if success:
            print(f"\n🎉 Enhanced migration completed successfully!")
        else:
            print(f"\n❌ Enhanced migration completed with errors.")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
